import 'package:flutter/material.dart';
import '../models/user_model.dart';

class ProfileAvatar extends StatelessWidget {
  final UserModel user;
  final double radius;
  final bool showBorder;
  final Color? borderColor;

  const ProfileAvatar({
    Key? key,
    required this.user,
    this.radius = 30,
    this.showBorder = false,
    this.borderColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: showBorder
          ? BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: borderColor ?? Theme.of(context).primaryColor,
                width: 2,
              ),
            )
          : null,
      child: CircleAvatar(
        radius: radius,
        backgroundColor: Theme.of(context).primaryColor,
        backgroundImage: user.profileImage != null
            ? AssetImage(user.profileImage!)
            : null,
        child: user.profileImage == null
            ? Text(
                user.prenom[0] + user.nom[0],
                style: TextStyle(
                  fontSize: radius * 0.8,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              )
            : null,
      ),
    );
  }
}
