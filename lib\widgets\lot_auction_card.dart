import 'package:flutter/material.dart';
import 'package:seatrace/models/lot_model.dart';

class LotAuctionCard extends StatefulWidget {
  final LotModel lot;
  final Function(double, DateTime) onStartAuction;

  const LotAuctionCard({
    Key? key,
    required this.lot,
    required this.onStartAuction,
  }) : super(key: key);

  @override
  State<LotAuctionCard> createState() => _LotAuctionCardState();
}

class _LotAuctionCardState extends State<LotAuctionCard> {
  final _prixController = TextEditingController();
  int _dureeHeures = 24;

  @override
  void dispose() {
    _prixController.dispose();
    super.dispose();
  }

  void _showAuctionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Lancer une enchère'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _prixController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Prix initial (DT)',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<int>(
              value: _dureeHeures,
              decoration: const InputDecoration(
                labelText: 'Durée de l\'enchère',
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(value: 1, child: Text('1 heure')),
                DropdownMenuItem(value: 6, child: Text('6 heures')),
                DropdownMenuItem(value: 12, child: Text('12 heures')),
                DropdownMenuItem(value: 24, child: Text('24 heures')),
                DropdownMenuItem(value: 48, child: Text('48 heures')),
              ],
              onChanged: (value) {
                setState(() {
                  _dureeHeures = value!;
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              if (_prixController.text.isNotEmpty) {
                final prix = double.parse(_prixController.text);
                final dateFin = DateTime.now().add(Duration(hours: _dureeHeures));
                widget.onStartAuction(prix, dateFin);
                Navigator.of(context).pop();
              }
            },
            child: const Text('Lancer'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.lot.identifiant,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'Validé',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: Colors.grey[300],
                  ),
                  child: const Icon(
                    Icons.image,
                    size: 40,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.lot.especeNom,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Quantité: ${widget.lot.quantite}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        'Poids: ${widget.lot.poids} kg',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        'Température: ${widget.lot.temperature}°C',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'Lieu: ${widget.lot.lieu}',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _showAuctionDialog,
                icon: const Icon(Icons.gavel),
                label: const Text('Lancer une enchère'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.secondary,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
