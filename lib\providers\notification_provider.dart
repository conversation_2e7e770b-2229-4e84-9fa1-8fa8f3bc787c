import 'package:flutter/material.dart';
import '../models/notification_model.dart';
import '../services/api_service.dart';

class NotificationProvider with ChangeNotifier {
  List<NotificationModel> _notifications = [];
  bool _isLoading = false;
  String? _error;

  List<NotificationModel> get notifications => _notifications;
  bool get isLoading => _isLoading;
  String? get error => _error;

  int get unreadCount => _notifications.where((n) => !n.isRead).length;

  Future<void> loadNotifications(String userId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await ApiService.get('/notifications/$userId');
      if (response['success']) {
        _notifications = (response['data'] as List)
            .map((json) => NotificationModel.fromJson(json))
            .toList();
        
        // Trier par date (plus récent en premier)
        _notifications.sort((a, b) => b.date.compareTo(a.date));
      } else {
        _error = response['message'] ?? 'Erreur lors du chargement des notifications';
      }
    } catch (e) {
      _error = 'Erreur de connexion: $e';
      // Données de test pour le développement
      _loadTestNotifications();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void _loadTestNotifications() {
    _notifications = [
      NotificationModel(
        id: '1',
        title: 'Lot vendu !',
        message: 'Votre lot de Rouget (150kg) a été acheté par Rayen Ben Ali',
        type: 'lot_sold',
        date: DateTime.now().subtract(const Duration(hours: 2)),
        isRead: false,
        data: {
          'lotId': 'LOT-1748840152154',
          'clientName': 'Rayen Ben Ali',
          'fishType': 'Rouget',
          'quantity': 150,
          'price': 450.0,
        },
      ),
      NotificationModel(
        id: '2',
        title: 'Lot validé',
        message: 'Votre lot de Thon a été validé par le vétérinaire',
        type: 'lot_validated',
        date: DateTime.now().subtract(const Duration(hours: 5)),
        isRead: true,
        data: {
          'lotId': 'LOT-1748840407044',
          'vetName': 'Dr. Insaf Mejri',
          'fishType': 'Thon',
        },
      ),
      NotificationModel(
        id: '3',
        title: 'Nouveau lot déposé',
        message: 'Votre lot de Daurade a été enregistré avec succès',
        type: 'lot_created',
        date: DateTime.now().subtract(const Duration(days: 1)),
        isRead: true,
        data: {
          'lotId': 'LOT-1748840152155',
          'fishType': 'Daurade',
          'quantity': 80,
        },
      ),
      NotificationModel(
        id: '4',
        title: 'Lot vendu !',
        message: 'Votre lot de Crevettes (50kg) a été acheté par Ahmed Trabelsi',
        type: 'lot_sold',
        date: DateTime.now().subtract(const Duration(days: 2)),
        isRead: false,
        data: {
          'lotId': 'LOT-1748840152156',
          'clientName': 'Ahmed Trabelsi',
          'fishType': 'Crevettes',
          'quantity': 50,
          'price': 200.0,
        },
      ),
    ];
  }

  Future<void> markAsRead(String notificationId) async {
    try {
      final response = await ApiService.put('/notifications/$notificationId/read', {});
      if (response['success']) {
        final index = _notifications.indexWhere((n) => n.id == notificationId);
        if (index != -1) {
          _notifications[index] = _notifications[index].copyWith(isRead: true);
          notifyListeners();
        }
      }
    } catch (e) {
      // En cas d'erreur, marquer localement
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        _notifications[index] = _notifications[index].copyWith(isRead: true);
        notifyListeners();
      }
    }
  }

  Future<void> markAllAsRead() async {
    try {
      final userId = _notifications.isNotEmpty ? 'current_user' : '';
      final response = await ApiService.put('/notifications/$userId/read-all', {});
      if (response['success']) {
        _notifications = _notifications.map((n) => n.copyWith(isRead: true)).toList();
        notifyListeners();
      }
    } catch (e) {
      // En cas d'erreur, marquer localement
      _notifications = _notifications.map((n) => n.copyWith(isRead: true)).toList();
      notifyListeners();
    }
  }

  void addNotification(NotificationModel notification) {
    _notifications.insert(0, notification);
    notifyListeners();
  }

  void clearNotifications() {
    _notifications.clear();
    notifyListeners();
  }
}
