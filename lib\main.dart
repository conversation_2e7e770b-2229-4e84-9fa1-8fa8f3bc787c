import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:seatrace/providers/auth_provider.dart';
import 'package:seatrace/providers/lot_provider.dart';
import 'package:seatrace/providers/auction_provider.dart';
import 'package:seatrace/screens/splash_screen.dart';
import 'package:camera/camera.dart';

List<CameraDescription> cameras = [];

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  try {
    cameras = await availableCameras();
  } on CameraException catch (e) {
    print('Error initializing camera: $e');
  }
  
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => LotProvider()),
        ChangeNotifierProvider(create: (_) => AuctionProvider()),
      ],
      child: const SeaTraceApp(),
    ),
  );
}

class SeaTraceApp extends StatelessWidget {
  const SeaTraceApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'SeaTrace',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primaryColor: const Color(0xFF2045B5),
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF2045B5),
          secondary: const Color(0xFF1EACC7),
        ),
        fontFamily: 'Poppins',
        appBarTheme: const AppBarTheme(
          backgroundColor: Color(0xFF2045B5),
          foregroundColor: Colors.white,
          elevation: 0,
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF1EACC7),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        ),
      ),
      home: const SplashScreen(),
    );
  }
}
