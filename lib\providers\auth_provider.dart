import 'package:flutter/material.dart';
import 'package:seatrace/models/user_model.dart';

class AuthProvider extends ChangeNotifier {
  UserModel? _currentUser;
  bool _isLoading = false;
  String? _error;
  
  UserModel? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isLoggedIn => _currentUser != null;
  
  // Simulation de l'authentification
  Future<bool> login(String email, String password) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      // Simulation d'un appel API
      await Future.delayed(const Duration(seconds: 2));
      
      // Données simulées basées sur les données fournies
      if (email == "<EMAIL>" && password == "password") {
        _currentUser = UserModel(
          id: "68346e6aa5e347d60aae73b8",
          email: "<EMAIL>",
          role: "ROLE_PECHEUR",
          nom: "Sfa<PERSON>",
          prenom: "<PERSON><PERSON><PERSON><PERSON>",
          telephone: "97274883",
          isValidated: true,
          isBlocked: false,
          cin: "13224789",
          matricule: "888",
          bateau: "Zouu",
          port: "Mahdia",
        );
      } else if (email == "<EMAIL>" && password == "password") {
        _currentUser = UserModel(
          id: "68346fd4a5e347d60aae73cb",
          email: "<EMAIL>",
          role: "ROLE_VETERINAIRE",
          nom: "Sfaxi",
          prenom: "Insaf",
          telephone: "25018443",
          isValidated: true,
          isBlocked: false,
          cin: "19985463",
          matricule: "456",
          port: "Cabinet Dr sfaxi a Mahdia",
        );
      } else if (email == "<EMAIL>" && password == "password") {
        _currentUser = UserModel(
          id: "68347159a5e347d60aae73e6",
          email: "<EMAIL>",
          role: "ROLE_MAREYEUR",
          nom: "Mareyeur",
          prenom: "Test",
          telephone: "12345678",
          isValidated: true,
          isBlocked: false,
        );
      } else if (email == "<EMAIL>" && password == "password") {
        _currentUser = UserModel(
          id: "68347249a5e347d60aae73fc",
          email: "<EMAIL>",
          role: "ROLE_CLIENT",
          nom: "Sfaxi",
          prenom: "Rayen",
          telephone: "26458091",
          isValidated: true,
          isBlocked: false,
        );
      } else {
        _error = "Email ou mot de passe incorrect";
        _isLoading = false;
        notifyListeners();
        return false;
      }
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }
  
  Future<bool> register(Map<String, dynamic> userData) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      // Simulation d'un appel API
      await Future.delayed(const Duration(seconds: 2));
      
      // Dans une vraie application, vous enverriez ces données à votre API
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }
  
  void logout() {
    _currentUser = null;
    notifyListeners();
  }
}
