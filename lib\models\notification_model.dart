class NotificationModel {
  final String id;
  final String title;
  final String message;
  final String type; // 'lot_sold', 'lot_validated', 'lot_rejected'
  final DateTime date;
  final bool isRead;
  final Map<String, dynamic> data; // Données supplémentaires (lot, client, prix, etc.)

  NotificationModel({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.date,
    this.isRead = false,
    this.data = const {},
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['_id'] is Map ? json['_id']['\$oid'] : json['_id'],
      title: json['title'],
      message: json['message'],
      type: json['type'],
      date: DateTime.parse(json['date']),
      isRead: json['isRead'] ?? false,
      data: json['data'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'title': title,
      'message': message,
      'type': type,
      'date': date.toIso8601String(),
      'isRead': isRead,
      'data': data,
    };
  }

  NotificationModel copyWith({
    String? id,
    String? title,
    String? message,
    String? type,
    DateTime? date,
    bool? isRead,
    Map<String, dynamic>? data,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      date: date ?? this.date,
      isRead: isRead ?? this.isRead,
      data: data ?? this.data,
    );
  }
}
