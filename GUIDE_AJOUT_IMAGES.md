# 📸 Guide d'Ajout des Images de Poissons

## 🎯 Objectif

Ce guide vous explique comment ajouter les vraies images des poissons dans l'historique du pêcheur.

## 📁 Structure Actuelle

L'application est configurée pour charger les images depuis :
```
assets/lot/
├── daurade.jpg
├── thon.jpg
├── calamar.jpg
├── espadon.jpg
├── scorpaena.jpg
└── crevette.jpg
```

## 📋 Étapes pour Ajouter les Images

### **Étape 1 : Préparer vos Images**

#### **Spécifications Recommandées :**
- **Format** : JPG ou PNG (JPG recommandé pour la taille)
- **Dimensions** : 300x200 pixels (ratio 3:2)
- **Taille** : Maximum 500KB par image
- **Qualité** : Bonne résolution, bien éclairée

#### **Noms de Fichiers Exacts :**
- `daurade.jpg` - Image de daurade
- `thon.jpg` - Image de thon
- `calamar.jpg` - Image de calamar
- `espadon.jpg` - Image d'espadon
- `scorpaena.jpg` - Image de scorpaena (rascasse)
- `crevette.jpg` - Image de crevettes

### **Étape 2 : Remplacer les Fichiers Placeholder**

#### **Supprimer les anciens fichiers :**
```bash
# Naviguez vers le dossier du projet
cd C:\SeaTrace\assets\lot\

# Supprimez les fichiers .txt
del *.txt
```

#### **Ajouter vos images :**
1. Copiez vos images dans le dossier `C:\SeaTrace\assets\lot\`
2. Vérifiez que les noms correspondent exactement
3. Vérifiez que les extensions sont correctes (.jpg ou .png)

### **Étape 3 : Vérifier la Configuration**

#### **Vérifiez pubspec.yaml :**
```yaml
flutter:
  assets:
    - assets/images/
    - assets/lot/  # ✅ Déjà configuré
```

### **Étape 4 : Recompiler l'Application**

#### **Nettoyer et recompiler :**
```bash
# Nettoyer le projet
flutter clean

# Récupérer les dépendances
flutter pub get

# Compiler l'APK
flutter build apk --release
```

## 🖼️ Sources d'Images Recommandées

### **Images Libres de Droits :**
- **Unsplash** : https://unsplash.com/s/photos/fish
- **Pexels** : https://www.pexels.com/search/fish/
- **Pixabay** : https://pixabay.com/images/search/fish/

### **Recherches Spécifiques :**
- **Daurade** : "sea bream fish", "dorade fish"
- **Thon** : "tuna fish", "bluefin tuna"
- **Calamar** : "squid", "calamari"
- **Espadon** : "swordfish", "marlin"
- **Scorpaena** : "scorpion fish", "rascasse"
- **Crevettes** : "shrimp", "prawns"

## 🛠️ Outils de Redimensionnement

### **Outils en Ligne :**
- **Canva** : https://www.canva.com/resize/
- **ResizeImage** : https://resizeimage.net/
- **ILoveIMG** : https://www.iloveimg.com/resize-image

### **Logiciels :**
- **GIMP** (gratuit)
- **Paint.NET** (gratuit)
- **Photoshop** (payant)

## 📱 Test des Images

### **Après avoir ajouté les images :**

1. **Compilez l'application** avec `flutter build apk --release`
2. **Installez l'APK** sur votre téléphone
3. **Connectez-vous** avec le compte pêcheur
4. **Accédez à l'historique** (icône ⏰)
5. **Vérifiez** que les images s'affichent correctement

## ⚠️ Résolution de Problèmes

### **Si les images ne s'affichent pas :**

#### **Vérifiez les noms de fichiers :**
```bash
# Listez les fichiers dans le dossier
dir C:\SeaTrace\assets\lot\
```

#### **Vérifiez la casse :**
- Les noms doivent être en minuscules
- Pas d'espaces dans les noms
- Extensions correctes (.jpg ou .png)

#### **Vérifiez la compilation :**
```bash
# Nettoyez complètement
flutter clean
rm -rf build/

# Recompilez
flutter pub get
flutter build apk --release
```

### **Si l'application plante :**
- Vérifiez que les images ne sont pas corrompues
- Vérifiez la taille des images (max 500KB)
- Vérifiez le format (JPG/PNG uniquement)

## 🎨 Fallback Automatique

### **Si une image est manquante :**
L'application affichera automatiquement :
- Une icône spécialisée pour chaque espèce
- Le nom de l'espèce
- Un fond gris avec bordure

### **Icônes de fallback :**
- 🍽️ **Daurade** : Icône repas
- 🍽️ **Thon** : Icône dîner
- 🌊 **Calamar** : Icône vagues
- 🎣 **Espadon** : Icône pêche
- 🦠 **Scorpaena** : Icône virus (épines)
- 🐾 **Crevettes** : Icône sans cruauté

## 📊 Exemple de Résultat

### **Avec images :**
```
┌─────────────────────────────────┐
│ [IMAGE: Daurade dorée]          │
│ Espèce: Daurade                 │
│ Poids: 200kg                    │
│ Lieu: Mahdia                    │
└─────────────────────────────────┘
```

### **Sans image (fallback) :**
```
┌─────────────────────────────────┐
│ [🍽️ DAURADE]                   │
│ Espèce: Daurade                 │
│ Poids: 200kg                    │
│ Lieu: Mahdia                    │
└─────────────────────────────────┘
```

## 🚀 Script d'Automatisation

### **Script PowerShell pour faciliter le processus :**

```powershell
# Créez ce fichier : update_images.ps1

Write-Host "=== Mise à jour des images de poissons ===" -ForegroundColor Green

# Vérifier la présence des images
$images = @("daurade.jpg", "thon.jpg", "calamar.jpg", "espadon.jpg", "scorpaena.jpg", "crevette.jpg")
$lotPath = "assets\lot"

foreach ($image in $images) {
    $imagePath = Join-Path $lotPath $image
    if (Test-Path $imagePath) {
        Write-Host "✓ $image trouvée" -ForegroundColor Green
    } else {
        Write-Host "❌ $image manquante" -ForegroundColor Red
    }
}

# Recompiler si toutes les images sont présentes
$allPresent = $true
foreach ($image in $images) {
    if (-not (Test-Path (Join-Path $lotPath $image))) {
        $allPresent = $false
        break
    }
}

if ($allPresent) {
    Write-Host "Toutes les images sont présentes. Compilation..." -ForegroundColor Yellow
    flutter clean
    flutter pub get
    flutter build apk --release
    Write-Host "✅ Compilation terminée !" -ForegroundColor Green
} else {
    Write-Host "⚠️ Ajoutez les images manquantes avant de compiler" -ForegroundColor Yellow
}
```

## 🎯 Résultat Final

Après avoir suivi ce guide, votre application aura :
- ✅ **6 images de poissons** dans l'historique
- ✅ **Interface visuelle riche** et professionnelle
- ✅ **Fallback intelligent** en cas d'image manquante
- ✅ **Performance optimisée** avec images compressées

**Votre historique pêcheur sera visuellement impressionnant ! 🎣📸**
