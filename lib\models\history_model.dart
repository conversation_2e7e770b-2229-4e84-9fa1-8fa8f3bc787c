class HistoryItemModel {
  final String id;
  final String userId;
  final String
      type; // 'lot_scanned', 'lot_validated', 'auction_created', 'bid_placed'
  final String title;
  final String description;
  final DateTime date;
  final Map<String, dynamic> data; // Données spécifiques selon le type
  final String status; // 'success', 'pending', 'failed', 'completed'

  HistoryItemModel({
    required this.id,
    required this.userId,
    required this.type,
    required this.title,
    required this.description,
    required this.date,
    required this.data,
    required this.status,
  });

  factory HistoryItemModel.fromJson(Map<String, dynamic> json) {
    return HistoryItemModel(
      id: json['id'],
      userId: json['userId'],
      type: json['type'],
      title: json['title'],
      description: json['description'],
      date: DateTime.parse(json['date']),
      data: json['data'] ?? {},
      status: json['status'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'type': type,
      'title': title,
      'description': description,
      'date': date.toIso8601String(),
      'data': data,
      'status': status,
    };
  }
}

// Modèle spécifique pour l'historique des lots scannés (Pêcheur)
class ScannedLotHistory extends HistoryItemModel {
  ScannedLotHistory({
    required String id,
    required String userId,
    required String title,
    required String description,
    required DateTime date,
    required String lotId,
    required String especeNom,
    required double poids,
    required String lieu,
    required String status,
    String? imagePath,
  }) : super(
          id: id,
          userId: userId,
          type: 'lot_scanned',
          title: title,
          description: description,
          date: date,
          data: {
            'lotId': lotId,
            'especeNom': especeNom,
            'poids': poids,
            'lieu': lieu,
            'imagePath': imagePath,
          },
          status: status,
        );
}

// Modèle spécifique pour l'historique des validations (Vétérinaire)
class ValidatedLotHistory extends HistoryItemModel {
  ValidatedLotHistory({
    required String id,
    required String userId,
    required String title,
    required String description,
    required DateTime date,
    required String lotId,
    required String especeNom,
    required bool isApproved,
    required String? comment,
    String? imagePath,
  }) : super(
          id: id,
          userId: userId,
          type: 'lot_validated',
          title: title,
          description: description,
          date: date,
          data: {
            'lotId': lotId,
            'especeNom': especeNom,
            'isApproved': isApproved,
            'comment': comment,
            'imagePath': imagePath,
          },
          status: isApproved ? 'approved' : 'rejected',
        );
}

// Modèle spécifique pour l'historique des enchères créées (Mareyeur)
class CreatedAuctionHistory extends HistoryItemModel {
  CreatedAuctionHistory({
    required String id,
    required String userId,
    required String title,
    required String description,
    required DateTime date,
    required String auctionId,
    required String lotId,
    required String especeNom,
    required double prixInitial,
    required DateTime dateFin,
    required String status,
  }) : super(
          id: id,
          userId: userId,
          type: 'auction_created',
          title: title,
          description: description,
          date: date,
          data: {
            'auctionId': auctionId,
            'lotId': lotId,
            'especeNom': especeNom,
            'prixInitial': prixInitial,
            'dateFin': dateFin.toIso8601String(),
          },
          status: status,
        );
}

// Modèle spécifique pour l'historique des enchères participées (Client)
class BidHistory extends HistoryItemModel {
  BidHistory({
    required String id,
    required String userId,
    required String title,
    required String description,
    required DateTime date,
    required String auctionId,
    required String lotId,
    required String especeNom,
    required double montant,
    required bool isWinning,
    required String status,
  }) : super(
          id: id,
          userId: userId,
          type: 'bid_placed',
          title: title,
          description: description,
          date: date,
          data: {
            'auctionId': auctionId,
            'lotId': lotId,
            'especeNom': especeNom,
            'montant': montant,
            'isWinning': isWinning,
          },
          status: status,
        );
}
