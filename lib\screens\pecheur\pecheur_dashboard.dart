import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:seatrace/providers/auth_provider.dart';
import 'package:seatrace/providers/notification_provider.dart';
import 'package:seatrace/models/notification_model.dart';
import 'package:seatrace/screens/auth/login_screen.dart';
import 'package:seatrace/screens/pecheur/scan_fish_screen.dart';
import 'package:seatrace/screens/history/history_screen.dart';
import 'package:seatrace/widgets/notification_card.dart';
import 'package:seatrace/widgets/profile_avatar.dart';

class PecheurDashboard extends StatefulWidget {
  const PecheurDashboard({Key? key}) : super(key: key);

  @override
  State<PecheurDashboard> createState() => _PecheurDashboardState();
}

class _PecheurDashboardState extends State<PecheurDashboard> {
  @override
  void initState() {
    super.initState();
    _loadNotifications();
  }

  Future<void> _loadNotifications() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final notificationProvider =
        Provider.of<NotificationProvider>(context, listen: false);

    if (authProvider.isLoggedIn) {
      await notificationProvider
          .loadNotifications(authProvider.currentUser!.id);
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final notificationProvider = Provider.of<NotificationProvider>(context);

    if (!authProvider.isLoggedIn) {
      return const LoginScreen();
    }

    final user = authProvider.currentUser!;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Tableau de bord'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add_alert),
            onPressed: () {
              // Ajouter une notification de test
              notificationProvider.addNotification(
                NotificationModel(
                  id: DateTime.now().millisecondsSinceEpoch.toString(),
                  title: 'Test - Lot vendu !',
                  message: 'Votre lot de test a été acheté par un client test',
                  type: 'lot_sold',
                  date: DateTime.now(),
                  isRead: false,
                  data: {
                    'lotId':
                        'LOT-TEST-${DateTime.now().millisecondsSinceEpoch}',
                    'clientName': 'Client Test',
                    'fishType': 'Poisson Test',
                    'quantity': 100,
                    'price': 300.0,
                  },
                ),
              );
            },
            tooltip: 'Ajouter notification test',
          ),
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(builder: (_) => const HistoryScreen()),
              );
            },
            tooltip: 'Historique des lots scannés',
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () {
              authProvider.logout();
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(builder: (_) => const LoginScreen()),
              );
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadNotifications,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Card(
                  elevation: 4,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            ProfileAvatar(
                              user: user,
                              radius: 30,
                            ),
                            const SizedBox(width: 16),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '${user.prenom} ${user.nom}',
                                  style: const TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'Pêcheur',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        const Divider(height: 30),
                        _buildInfoRow('Bateau', user.bateau ?? 'Non spécifié'),
                        _buildInfoRow('Port', user.port ?? 'Non spécifié'),
                        _buildInfoRow(
                            'Matricule', user.matricule ?? 'Non spécifié'),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Notifications',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Row(
                      children: [
                        if (notificationProvider.unreadCount > 0)
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              '${notificationProvider.unreadCount}',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        const SizedBox(width: 8),
                        Text(
                          '${notificationProvider.notifications.length} notifications',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                if (notificationProvider.isLoading)
                  const Center(
                    child: CircularProgressIndicator(),
                  )
                else if (notificationProvider.notifications.isEmpty)
                  Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const SizedBox(height: 40),
                        Icon(
                          Icons.notifications_none,
                          size: 80,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Aucune notification',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Vous serez notifié quand vos lots seront achetés',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey[500],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  )
                else
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: notificationProvider.notifications.length,
                    itemBuilder: (context, index) {
                      final notification =
                          notificationProvider.notifications[index];
                      return NotificationCard(
                        notification: notification,
                        onMarkAsRead: () {
                          notificationProvider.markAsRead(notification.id);
                        },
                        onTap: () {
                          // Optionnel: naviguer vers les détails du lot vendu
                          if (notification.type == 'lot_sold') {
                            _showNotificationDetails(notification);
                          }
                        },
                      );
                    },
                  ),
              ],
            ),
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.of(context).push(
            MaterialPageRoute(builder: (_) => const ScanFishScreen()),
          );
        },
        icon: const Icon(Icons.camera_alt),
        label: const Text('Scanner un poisson'),
        backgroundColor: Theme.of(context).colorScheme.secondary,
      ),
    );
  }

  void _showNotificationDetails(notification) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(notification.title),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(notification.message),
              if (notification.data.isNotEmpty) ...[
                const SizedBox(height: 16),
                const Text(
                  'Détails:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                if (notification.data['clientName'] != null)
                  Text('Client: ${notification.data['clientName']}'),
                if (notification.data['fishType'] != null)
                  Text('Poisson: ${notification.data['fishType']}'),
                if (notification.data['quantity'] != null)
                  Text('Quantité: ${notification.data['quantity']} kg'),
                if (notification.data['price'] != null)
                  Text('Prix: ${notification.data['price']} TND'),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Fermer'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
