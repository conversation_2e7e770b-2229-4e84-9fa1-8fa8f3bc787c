import 'package:flutter/material.dart';
import 'package:seatrace/models/history_model.dart';

class HistoryProvider extends ChangeNotifier {
  List<HistoryItemModel> _history = [];
  bool _isLoading = false;
  String? _error;

  List<HistoryItemModel> get history => _history;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Obtenir l'historique pour un utilisateur spécifique
  List<HistoryItemModel> getHistoryForUser(String userId) {
    return _history.where((item) => item.userId == userId).toList()
      ..sort((a, b) => b.date.compareTo(a.date)); // Tri par date décroissante
  }

  // Obtenir l'historique par type pour un utilisateur
  List<HistoryItemModel> getHistoryByType(String userId, String type) {
    return _history
        .where((item) => item.userId == userId && item.type == type)
        .toList()
      ..sort((a, b) => b.date.compareTo(a.date));
  }

  // Obtenir les lots validés pour un vétérinaire
  List<HistoryItemModel> getValidatedLots(String userId) {
    return _history
        .where((item) =>
            item.userId == userId &&
            item.type == 'lot_validated' &&
            item.data['isApproved'] == true)
        .toList()
      ..sort((a, b) => b.date.compareTo(a.date));
  }

  // Obtenir les lots refusés pour un vétérinaire
  List<HistoryItemModel> getRejectedLots(String userId) {
    return _history
        .where((item) =>
            item.userId == userId &&
            item.type == 'lot_validated' &&
            item.data['isApproved'] == false)
        .toList()
      ..sort((a, b) => b.date.compareTo(a.date));
  }

  // Charger l'historique des lots scannés (Pêcheur)
  Future<void> loadScannedLotsHistory(String userId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulation d'un appel API
      await Future.delayed(const Duration(seconds: 1));

      // Données simulées pour l'historique des lots scannés
      final scannedHistory = [
        ScannedLotHistory(
          id: "hist_scan_1",
          userId: userId,
          title: "Lot de Daurade scanné",
          description: "Scan réussi - 200kg de Daurade à Mahdia",
          date: DateTime.now().subtract(const Duration(days: 1)),
          lotId: "lot_daurade_1",
          especeNom: "Daurade",
          poids: 200.0,
          lieu: "Mahdia",
          status: "completed",
          imagePath: "assets/lot/daurade.jpg",
        ),
        ScannedLotHistory(
          id: "hist_scan_2",
          userId: userId,
          title: "Lot de Thon scanné",
          description: "Scan réussi - 500kg de Thon à Monastir",
          date: DateTime.now().subtract(const Duration(days: 2)),
          lotId: "lot_thon_1",
          especeNom: "Thon",
          poids: 500.0,
          lieu: "Monastir",
          status: "completed",
          imagePath: "assets/lot/thon.jpg",
        ),
        ScannedLotHistory(
          id: "hist_scan_3",
          userId: userId,
          title: "Lot de Calamar scanné",
          description: "Scan réussi - 80kg de Calamar à Sfax",
          date: DateTime.now().subtract(const Duration(days: 3)),
          lotId: "lot_calamar_1",
          especeNom: "Calamar",
          poids: 80.0,
          lieu: "Sfax",
          status: "completed",
          imagePath: "assets/lot/calamar.jpg",
        ),
        ScannedLotHistory(
          id: "hist_scan_4",
          userId: userId,
          title: "Lot d'Espadon scanné",
          description: "Scan réussi - 300kg d'Espadon à Sousse",
          date: DateTime.now().subtract(const Duration(days: 5)),
          lotId: "lot_espadon_1",
          especeNom: "Espadon",
          poids: 300.0,
          lieu: "Sousse",
          status: "completed",
          imagePath: "assets/lot/espadon.jpg",
        ),
        ScannedLotHistory(
          id: "hist_scan_5",
          userId: userId,
          title: "Lot de Scorpaena scanné",
          description: "Scan réussi - 120kg de Scorpaena à Bizerte",
          date: DateTime.now().subtract(const Duration(days: 6)),
          lotId: "lot_scorpaena_1",
          especeNom: "Scorpaena",
          poids: 120.0,
          lieu: "Bizerte",
          status: "completed",
          imagePath: "assets/lot/scorpaena.jpg",
        ),
        ScannedLotHistory(
          id: "hist_scan_6",
          userId: userId,
          title: "Lot de Crevettes scanné",
          description: "Scan réussi - 50kg de Crevettes à Gabès",
          date: DateTime.now().subtract(const Duration(days: 8)),
          lotId: "lot_crevette_1",
          especeNom: "Crevette",
          poids: 50.0,
          lieu: "Gabès",
          status: "completed",
          imagePath: "assets/lot/crevette.jpg",
        ),
      ];

      // Remplacer l'historique existant pour ce type
      _history.removeWhere(
          (item) => item.userId == userId && item.type == 'lot_scanned');
      _history.addAll(scannedHistory);

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Charger l'historique des validations (Vétérinaire)
  Future<void> loadValidatedLotsHistory(String userId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await Future.delayed(const Duration(seconds: 1));

      // Historique des validations correspondant aux lots du pêcheur
      // 4 lots validés et 2 lots refusés
      final validatedHistory = [
        ValidatedLotHistory(
          id: "hist_valid_1",
          userId: userId,
          title: "Lot de Daurade validé",
          description: "Lot approuvé - Qualité excellente",
          date: DateTime.now().subtract(const Duration(days: 1, hours: 2)),
          lotId: "lot_daurade_1",
          especeNom: "Daurade",
          isApproved: true,
          comment: "Poisson frais, température correcte, traçabilité complète",
          imagePath: "assets/lot/daurade.jpg",
        ),
        ValidatedLotHistory(
          id: "hist_valid_2",
          userId: userId,
          title: "Lot de Thon validé",
          description: "Lot approuvé - Excellent état",
          date: DateTime.now().subtract(const Duration(days: 2, hours: 1)),
          lotId: "lot_thon_1",
          especeNom: "Thon",
          isApproved: true,
          comment: "Qualité premium, respect de la chaîne du froid",
          imagePath: "assets/lot/thon.jpg",
        ),
        ValidatedLotHistory(
          id: "hist_valid_3",
          userId: userId,
          title: "Lot de Calamar refusé",
          description: "Lot refusé - Problème de fraîcheur",
          date: DateTime.now().subtract(const Duration(days: 3, hours: 3)),
          lotId: "lot_calamar_1",
          especeNom: "Calamar",
          isApproved: false,
          comment: "Signes de détérioration, odeur suspecte",
          imagePath: "assets/lot/calamar.jpg",
        ),
        ValidatedLotHistory(
          id: "hist_valid_4",
          userId: userId,
          title: "Lot d'Espadon validé",
          description: "Lot approuvé - Conforme aux normes",
          date: DateTime.now().subtract(const Duration(days: 5, hours: 1)),
          lotId: "lot_espadon_1",
          especeNom: "Espadon",
          isApproved: true,
          comment: "Respect des normes sanitaires, excellent état",
          imagePath: "assets/lot/espadon.jpg",
        ),
        ValidatedLotHistory(
          id: "hist_valid_5",
          userId: userId,
          title: "Lot de Scorpaena refusé",
          description: "Lot refusé - Température inadéquate",
          date: DateTime.now().subtract(const Duration(days: 6, hours: 2)),
          lotId: "lot_scorpaena_1",
          especeNom: "Scorpaena",
          isApproved: false,
          comment:
              "Température de conservation non respectée, risque sanitaire",
          imagePath: "assets/lot/scorpaena.jpg",
        ),
        ValidatedLotHistory(
          id: "hist_valid_6",
          userId: userId,
          title: "Lot de Crevettes validé",
          description: "Lot approuvé - Qualité optimale",
          date: DateTime.now().subtract(const Duration(days: 8, hours: 1)),
          lotId: "lot_crevette_1",
          especeNom: "Crevette",
          isApproved: true,
          comment: "Fraîcheur parfaite, respect des normes d'hygiène",
          imagePath: "assets/lot/crevette.jpg",
        ),
      ];

      _history.removeWhere(
          (item) => item.userId == userId && item.type == 'lot_validated');
      _history.addAll(validatedHistory);

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Charger l'historique des enchères créées (Mareyeur)
  Future<void> loadCreatedAuctionsHistory(String userId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await Future.delayed(const Duration(seconds: 1));

      // Historique basé sur les lots validés par le vétérinaire
      // Chaque lot validé devient automatiquement une enchère
      final auctionHistory = [
        // Lot de Daurade validé → Enchère terminée avec gagnant
        CreatedAuctionHistory(
          id: "hist_auction_1",
          userId: userId,
          title: "Enchère Daurade terminée",
          description: "Enchère terminée - Vendu 280 DT à Rayen",
          date: DateTime.now().subtract(const Duration(days: 1, hours: 3)),
          auctionId: "auction_daurade_1",
          lotId: "lot_daurade_1",
          especeNom: "Daurade",
          prixInitial: 200.0,
          dateFin: DateTime.now().subtract(const Duration(hours: 6)),
          status: "completed",
          imagePath: "assets/lot/daurade.jpg",
          prixFinal: 280.0,
          clientGagnant: "client_rayen_1",
          clientGagnantNom: "Rayen",
        ),
        // Lot de Thon validé → Enchère terminée avec gagnant
        CreatedAuctionHistory(
          id: "hist_auction_2",
          userId: userId,
          title: "Enchère Thon terminée",
          description: "Enchère terminée - Vendu 950 DT à Rayen",
          date: DateTime.now().subtract(const Duration(days: 2, hours: 2)),
          auctionId: "auction_thon_1",
          lotId: "lot_thon_1",
          especeNom: "Thon",
          prixInitial: 800.0,
          dateFin: DateTime.now().subtract(const Duration(days: 1, hours: 12)),
          status: "completed",
          imagePath: "assets/lot/thon.jpg",
          prixFinal: 950.0,
          clientGagnant: "client_rayen_1",
          clientGagnantNom: "Rayen",
        ),
        // Lot d'Espadon validé → Enchère terminée avec gagnant
        CreatedAuctionHistory(
          id: "hist_auction_3",
          userId: userId,
          title: "Enchère Espadon terminée",
          description: "Enchère terminée - Vendu 420 DT à Rayen",
          date: DateTime.now().subtract(const Duration(days: 5, hours: 2)),
          auctionId: "auction_espadon_1",
          lotId: "lot_espadon_1",
          especeNom: "Espadon",
          prixInitial: 350.0,
          dateFin: DateTime.now().subtract(const Duration(days: 4, hours: 16)),
          status: "completed",
          imagePath: "assets/lot/espadon.jpg",
          prixFinal: 420.0,
          clientGagnant: "client_rayen_1",
          clientGagnantNom: "Rayen",
        ),
        // Lot de Crevettes validé → Enchère terminée avec gagnant
        CreatedAuctionHistory(
          id: "hist_auction_4",
          userId: userId,
          title: "Enchère Crevettes terminée",
          description: "Enchère terminée - Vendu 85 DT à Ahmed",
          date: DateTime.now().subtract(const Duration(days: 8, hours: 2)),
          auctionId: "auction_crevette_1",
          lotId: "lot_crevette_1",
          especeNom: "Crevette",
          prixInitial: 60.0,
          dateFin: DateTime.now().subtract(const Duration(days: 7, hours: 18)),
          status: "completed",
          imagePath: "assets/lot/crevette.jpg",
          prixFinal: 85.0,
          clientGagnant: "client_ahmed_1",
          clientGagnantNom: "Ahmed",
        ),
      ];

      _history.removeWhere(
          (item) => item.userId == userId && item.type == 'auction_created');
      _history.addAll(auctionHistory);

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Charger l'historique des enchères participées (Client)
  Future<void> loadBidHistory(String userId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await Future.delayed(const Duration(seconds: 1));

      final bidHistory = [
        BidHistory(
          id: "hist_bid_1",
          userId: userId,
          title: "Enchère sur Rouget",
          description: "Enchère placée - 120 DT (En cours)",
          date: DateTime.now().subtract(const Duration(minutes: 30)),
          auctionId: "auction1",
          lotId: "683d2ed832e8d0cdcbb75cd4",
          especeNom: "Rouget",
          montant: 120.0,
          isWinning: true,
          status: "active",
        ),
        BidHistory(
          id: "hist_bid_2",
          userId: userId,
          title: "Enchère sur Daurade",
          description: "Enchère perdue - 200 DT",
          date: DateTime.now().subtract(const Duration(days: 2)),
          auctionId: "auction_daurade_1",
          lotId: "lot_daurade_1",
          especeNom: "Daurade",
          montant: 200.0,
          isWinning: false,
          status: "lost",
        ),
        BidHistory(
          id: "hist_bid_3",
          userId: userId,
          title: "Enchère sur Thon",
          description: "Enchère gagnée - 950 DT",
          date: DateTime.now().subtract(const Duration(days: 5)),
          auctionId: "auction_thon_1",
          lotId: "lot_thon_1",
          especeNom: "Thon",
          montant: 950.0,
          isWinning: true,
          status: "won",
        ),
      ];

      _history.removeWhere(
          (item) => item.userId == userId && item.type == 'bid_placed');
      _history.addAll(bidHistory);

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Ajouter un nouvel élément à l'historique
  void addHistoryItem(HistoryItemModel item) {
    _history.add(item);
    notifyListeners();
  }

  // Effacer l'historique pour un utilisateur
  void clearHistoryForUser(String userId) {
    _history.removeWhere((item) => item.userId == userId);
    notifyListeners();
  }
}
