# Script pour copier l'APK SeaTrace vers le bureau
# Facilite le transfert manuel vers le téléphone

Write-Host "=== Copie APK SeaTrace vers Bureau ===" -ForegroundColor Green
Write-Host ""

# Chemins
$apkSource = "build\app\outputs\flutter-apk\app-release.apk"
$desktopPath = [Environment]::GetFolderPath("Desktop")
$apkDestination = Join-Path $desktopPath "SeaTrace-avec-images.apk"

# Vérifier que l'APK source existe
if (-not (Test-Path $apkSource)) {
    Write-Host "Erreur: APK non trouvé: $apkSource" -ForegroundColor Red
    exit 1
}

# Copier l'APK vers le bureau
try {
    Copy-Item $apkSource $apkDestination -Force
    
    $apkSize = (Get-Item $apkDestination).Length
    $apkSizeMB = [math]::Round($apkSize / 1MB, 1)
    
    Write-Host "Succes: APK copié vers le bureau" -ForegroundColor Green
    Write-Host "Fichier: $apkDestination" -ForegroundColor Cyan
    Write-Host "Taille: $apkSizeMB MB" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Instructions pour installation manuelle:" -ForegroundColor Yellow
    Write-Host "1. Connectez votre Samsung Galaxy S23 Ultra en USB" -ForegroundColor White
    Write-Host "2. Copiez le fichier APK du bureau vers le téléphone" -ForegroundColor White
    Write-Host "3. Sur le téléphone, ouvrez le fichier APK" -ForegroundColor White
    Write-Host "4. Autorisez l'installation depuis sources inconnues" -ForegroundColor White
    Write-Host "5. Suivez les instructions d'installation" -ForegroundColor White
    Write-Host ""
    Write-Host "Test de l'historique avec images:" -ForegroundColor Yellow
    Write-Host "- Email: <EMAIL>" -ForegroundColor White
    Write-Host "- Mot de passe: Zouhaier1*" -ForegroundColor White
    Write-Host "- Cliquez sur l'icône historique dans le dashboard" -ForegroundColor White
    
} catch {
    Write-Host "Erreur lors de la copie: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Appuyez sur une touche pour continuer..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
