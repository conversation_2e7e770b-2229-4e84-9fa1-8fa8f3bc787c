import 'package:flutter/material.dart';
import 'package:seatrace/models/lot_model.dart';

class LotProvider extends ChangeNotifier {
  List<LotModel> _lots = [];
  bool _isLoading = false;
  String? _error;

  // Liste globale de tous les lots soumis (simulation d'une base de données)
  static List<LotModel> _allSubmittedLots = [
    // Lot existant pour les tests
    LotModel(
      id: "683d2ed832e8d0cdcbb75cd4",
      identifiant: "LOT-1748840152154",
      photo: "/api/images/profile_1748840152313-952853274.heic",
      especeNom: "Rouget",
      quantite: 1,
      poids: 150,
      temperature: 4,
      lieu: "sfax",
      dateSoumission: DateTime.parse("2025-06-02T04:55:52.515Z"),
      isValidated: false,
      vendu: false,
      isAuctionActive: false,
      online: false,
      typeEnchere: "standard",
      userId: "68346e6aa5e347d60aae73b8", // <PERSON><PERSON><PERSON>er
      veterinaireId: "68346fd4a5e347d60aae73cb", // Insaf
      maryeurId: "68347159a5e347d60aae73e6", // Ali
      status: false,
    ),
  ];

  List<LotModel> get lots => _lots;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Obtenir les lots pour un pêcheur
  Future<void> fetchPecheurLots(String pecheurId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulation d'un appel API
      await Future.delayed(const Duration(seconds: 1));

      // Filtrer les lots de ce pêcheur depuis la liste globale
      _lots =
          _allSubmittedLots.where((lot) => lot.userId == pecheurId).toList();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Obtenir les lots en attente pour un vétérinaire
  Future<void> fetchVeterinairePendingLots(String veterinaireId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulation d'un appel API
      await Future.delayed(const Duration(seconds: 1));

      // Filtrer les lots assignés à ce vétérinaire et non encore validés
      _lots = _allSubmittedLots
          .where((lot) =>
              lot.veterinaireId == veterinaireId &&
              !lot.isValidated &&
              !lot.status)
          .toList();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Obtenir les lots validés pour un mareyeur
  Future<void> fetchMareyeurValidatedLots(String mareyeurId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulation d'un appel API
      await Future.delayed(const Duration(seconds: 1));

      // Filtrer les lots assignés à ce mareyeur et validés par le vétérinaire
      _lots = _allSubmittedLots
          .where((lot) =>
              lot.maryeurId == mareyeurId &&
              lot.isValidated &&
              lot.status &&
              !lot.vendu)
          .toList();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Créer un nouveau lot
  Future<bool> createLot(Map<String, dynamic> lotData) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulation d'un appel API
      await Future.delayed(const Duration(seconds: 2));

      // Créer un nouveau lot avec un ID unique
      final newLot = LotModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        identifiant: "LOT-${DateTime.now().millisecondsSinceEpoch}",
        photo: lotData['photo'] ?? "",
        especeNom: lotData['especeNom'] ?? "",
        quantite: lotData['quantite'] ?? 1,
        poids: lotData['poids']?.toDouble() ?? 0.0,
        temperature: lotData['temperature']?.toDouble() ?? 0.0,
        lieu: lotData['lieu'] ?? "",
        description: lotData['description'],
        dateSoumission: DateTime.now(),
        isValidated: false,
        vendu: false,
        isAuctionActive: false,
        online: false,
        typeEnchere: "standard",
        userId: lotData['user'] ?? "",
        veterinaireId: lotData['veterinaire'] ?? "",
        maryeurId: lotData['maryeur'] ?? "",
        status: false,
      );

      // Ajouter le nouveau lot à la liste globale
      _allSubmittedLots.add(newLot);

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Valider ou refuser un lot par un vétérinaire
  Future<bool> validateLot(String lotId, bool isApproved) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulation d'un appel API
      await Future.delayed(const Duration(seconds: 1));

      // Trouver et mettre à jour le lot dans la liste globale
      final lotIndex = _allSubmittedLots.indexWhere((lot) => lot.id == lotId);
      if (lotIndex != -1) {
        final lot = _allSubmittedLots[lotIndex];
        final updatedLot = LotModel(
          id: lot.id,
          identifiant: lot.identifiant,
          photo: lot.photo,
          especeNom: lot.especeNom,
          quantite: lot.quantite,
          poids: lot.poids,
          temperature: lot.temperature,
          lieu: lot.lieu,
          description: lot.description,
          dateSoumission: lot.dateSoumission,
          isValidated: isApproved,
          vendu: lot.vendu,
          isAuctionActive: lot.isAuctionActive,
          online: lot.online,
          typeEnchere: lot.typeEnchere,
          userId: lot.userId,
          veterinaireId: lot.veterinaireId,
          maryeurId: lot.maryeurId,
          status: isApproved,
        );
        _allSubmittedLots[lotIndex] = updatedLot;
      }

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }
}
