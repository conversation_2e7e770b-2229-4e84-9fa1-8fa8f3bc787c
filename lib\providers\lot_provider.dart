import 'package:flutter/material.dart';
import 'package:seatrace/models/lot_model.dart';
import 'package:seatrace/providers/history_provider.dart';
import 'package:seatrace/models/history_model.dart';

class LotProvider extends ChangeNotifier {
  List<LotModel> _lots = [];
  bool _isLoading = false;
  String? _error;

  // Liste globale de tous les lots soumis (simulation d'une base de données)
  static List<LotModel> _allSubmittedLots = [
    // Lot soumis par Zouhaier en attente de validation
    LotModel(
      id: "683d2ed832e8d0cdcbb75cd5",
      identifiant: "LOT-1749070284444",
      photo: "/api/images/profile_1749070284444.heic",
      especeNom: "Rouget",
      quantite: 1,
      poids: 50,
      temperature: 24,
      lieu: "mahdia",
      dateSoumission: DateTime.parse("2025-04-06T21:51:24.444Z"),
      isValidated: false,
      vendu: false,
      isAuctionActive: false,
      online: false,
      typeEnchere: "standard",
      userId: "68346e6aa5e347d60aae73b8", // <PERSON><PERSON><PERSON>er
      veterinaireId: "68346fd4a5e347d60aae73cb", // Insaf
      maryeurId: "68347159a5e347d60aae73e6", // Ali
      status: false,
    ),
  ];

  List<LotModel> get lots => _lots;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Obtenir les lots pour un pêcheur
  Future<void> fetchPecheurLots(String pecheurId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulation d'un appel API
      await Future.delayed(const Duration(seconds: 1));

      // Filtrer les lots de ce pêcheur depuis la liste globale
      _lots =
          _allSubmittedLots.where((lot) => lot.userId == pecheurId).toList();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Obtenir les lots en attente pour un vétérinaire
  Future<void> fetchVeterinairePendingLots(String veterinaireId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulation d'un appel API
      await Future.delayed(const Duration(seconds: 1));

      // Filtrer les lots assignés à ce vétérinaire et non encore validés
      _lots = _allSubmittedLots
          .where((lot) =>
              lot.veterinaireId == veterinaireId &&
              !lot.isValidated &&
              !lot.status)
          .toList();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Obtenir les lots validés pour un mareyeur
  Future<void> fetchMareyeurValidatedLots(String mareyeurId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulation d'un appel API
      await Future.delayed(const Duration(seconds: 1));

      // Filtrer les lots assignés à ce mareyeur et validés par le vétérinaire
      _lots = _allSubmittedLots
          .where((lot) =>
              lot.maryeurId == mareyeurId &&
              lot.isValidated &&
              lot.status &&
              !lot.vendu)
          .toList();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Créer un nouveau lot
  Future<bool> createLot(Map<String, dynamic> lotData) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulation d'un appel API
      await Future.delayed(const Duration(seconds: 2));

      // Créer un nouveau lot avec un ID unique
      final newLot = LotModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        identifiant: "LOT-${DateTime.now().millisecondsSinceEpoch}",
        photo: lotData['photo'] ?? "",
        especeNom: lotData['especeNom'] ?? "",
        quantite: lotData['quantite'] ?? 1,
        poids: lotData['poids']?.toDouble() ?? 0.0,
        temperature: lotData['temperature']?.toDouble() ?? 0.0,
        lieu: lotData['lieu'] ?? "",
        description: lotData['description'],
        dateSoumission: DateTime.now(),
        isValidated: false,
        vendu: false,
        isAuctionActive: false,
        online: false,
        typeEnchere: "standard",
        userId: lotData['user'] ?? "",
        veterinaireId: lotData['veterinaire'] ?? "",
        maryeurId: lotData['maryeur'] ?? "",
        status: false,
      );

      // Ajouter le nouveau lot à la liste globale
      _allSubmittedLots.add(newLot);

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Valider ou refuser un lot par un vétérinaire
  Future<bool> validateLot(String lotId, bool isApproved,
      HistoryProvider historyProvider, String veterinaireId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Simulation d'un appel API
      await Future.delayed(const Duration(seconds: 1));

      // Trouver le lot dans la liste globale
      final lotIndex = _allSubmittedLots.indexWhere((lot) => lot.id == lotId);
      if (lotIndex != -1) {
        final lot = _allSubmittedLots[lotIndex];

        // Créer l'entrée d'historique
        final historyItem = ValidatedLotHistory(
          id: "hist_${DateTime.now().millisecondsSinceEpoch}",
          userId: veterinaireId,
          title: isApproved
              ? "Lot de ${lot.especeNom} validé"
              : "Lot de ${lot.especeNom} refusé",
          description: isApproved
              ? "Lot approuvé - Qualité conforme"
              : "Lot refusé - Non conforme aux standards",
          date: DateTime.now(),
          lotId: lot.id,
          especeNom: lot.especeNom,
          isApproved: isApproved,
          comment: isApproved
              ? "Poisson frais, température correcte, traçabilité complète"
              : "Température non conforme ou qualité insuffisante",
          imagePath: _getImagePathForSpecies(lot.especeNom),
        );

        // Ajouter à l'historique
        historyProvider.addHistoryItem(historyItem);

        // Retirer le lot de la liste des lots en attente
        _allSubmittedLots.removeAt(lotIndex);

        // Mettre à jour la liste locale des lots pour le vétérinaire
        _lots.removeWhere((l) => l.id == lotId);
      }

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Méthode helper pour obtenir le chemin de l'image selon l'espèce
  String _getImagePathForSpecies(String espece) {
    switch (espece.toLowerCase()) {
      case 'daurade':
        return 'assets/lot/daurade.jpg';
      case 'thon':
        return 'assets/lot/thon.jpg';
      case 'rouget':
        return 'assets/lot/rouget.jpg';
      case 'calamar':
        return 'assets/lot/calamar.jpg';
      case 'espadon':
        return 'assets/lot/espadon.jpg';
      case 'scorpaena':
        return 'assets/lot/scorpaena.jpg';
      case 'crevette':
        return 'assets/lot/crevette.jpg';
      default:
        return 'assets/lot/default.jpg';
    }
  }
}
