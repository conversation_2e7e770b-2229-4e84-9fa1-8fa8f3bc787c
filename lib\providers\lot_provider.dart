import 'package:flutter/material.dart';
import 'package:seatrace/models/lot_model.dart';

class LotProvider extends ChangeNotifier {
  List<LotModel> _lots = [];
  bool _isLoading = false;
  String? _error;
  
  List<LotModel> get lots => _lots;
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  // Obtenir les lots pour un pêcheur
  Future<void> fetchPecheurLots(String pecheurId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      // Simulation d'un appel API
      await Future.delayed(const Duration(seconds: 1));
      
      // Données simulées basées sur les données fournies
      _lots = [
        LotModel(
          id: "683d2ed832e8d0cdcbb75cd4",
          identifiant: "LOT-1748840152154",
          photo: "/api/images/profile_1748840152313-952853274.heic",
          especeNom: "Rouget",
          quantite: 1,
          poids: 150,
          temperature: 4,
          lieu: "sfax",
          dateSoumission: DateTime.parse("2025-06-02T04:55:52.515Z"),
          isValidated: false,
          vendu: false,
          isAuctionActive: false,
          online: false,
          typeEnchere: "standard",
          userId: "68346e6aa5e347d60aae73b8",
          veterinaireId: "68346fd4a5e347d60aae73cb",
          maryeurId: "68347159a5e347d60aae73e6",
          status: false,
        ),
        LotModel(
          id: "683d2fd732e8d0cdcbb75d18",
          identifiant: "LOT-1748840407044",
          photo: "/api/images/profile_1748840407240-311832791.heic",
          especeNom: "Rouget",
          quantite: 1,
          poids: 150,
          temperature: 4,
          lieu: "sfax",
          dateSoumission: DateTime.parse("2025-06-02T05:00:07.378Z"),
          isValidated: false,
          vendu: false,
          isAuctionActive: false,
          online: false,
          typeEnchere: "standard",
          userId: "68346e6aa5e347d60aae73b8",
          veterinaireId: "68346fd4a5e347d60aae73cb",
          maryeurId: "68347159a5e347d60aae73e6",
          status: false,
        ),
      ];
      
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }
  
  // Obtenir les lots en attente pour un vétérinaire
  Future<void> fetchVeterinairePendingLots(String veterinaireId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      // Simulation d'un appel API
      await Future.delayed(const Duration(seconds: 1));
      
      // Données simulées basées sur les données fournies
      _lots = [
        LotModel(
          id: "683d2ed832e8d0cdcbb75cd4",
          identifiant: "LOT-1748840152154",
          photo: "/api/images/profile_1748840152313-952853274.heic",
          especeNom: "Rouget",
          quantite: 1,
          poids: 150,
          temperature: 4,
          lieu: "sfax",
          dateSoumission: DateTime.parse("2025-06-02T04:55:52.515Z"),
          isValidated: false,
          vendu: false,
          isAuctionActive: false,
          online: false,
          typeEnchere: "standard",
          userId: "68346e6aa5e347d60aae73b8",
          veterinaireId: "68346fd4a5e347d60aae73cb",
          maryeurId: "68347159a5e347d60aae73e6",
          status: false,
        ),
      ];
      
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }
  
  // Obtenir les lots validés pour un mareyeur
  Future<void> fetchMareyeurValidatedLots(String mareyeurId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      // Simulation d'un appel API
      await Future.delayed(const Duration(seconds: 1));
      
      // Données simulées
      _lots = [
        LotModel(
          id: "683d2ed832e8d0cdcbb75cd4",
          identifiant: "LOT-1748840152154",
          photo: "/api/images/profile_1748840152313-952853274.heic",
          especeNom: "Rouget",
          quantite: 1,
          poids: 150,
          temperature: 4,
          lieu: "sfax",
          dateSoumission: DateTime.parse("2025-06-02T04:55:52.515Z"),
          isValidated: true,
          vendu: false,
          isAuctionActive: false,
          online: false,
          typeEnchere: "standard",
          userId: "68346e6aa5e347d60aae73b8",
          veterinaireId: "68346fd4a5e347d60aae73cb",
          maryeurId: "68347159a5e347d60aae73e6",
          status: true,
        ),
      ];
      
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }
  
  // Créer un nouveau lot
  Future<bool> createLot(Map<String, dynamic> lotData) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      // Simulation d'un appel API
      await Future.delayed(const Duration(seconds: 2));
      
      // Dans une vraie application, vous enverriez ces données à votre API
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }
  
  // Valider ou refuser un lot par un vétérinaire
  Future<bool> validateLot(String lotId, bool isApproved) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      // Simulation d'un appel API
      await Future.delayed(const Duration(seconds: 1));
      
      // Dans une vraie application, vous enverriez ces données à votre API
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }
}
