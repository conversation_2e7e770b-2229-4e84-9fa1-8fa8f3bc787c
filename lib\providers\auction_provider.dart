import 'package:flutter/material.dart';
import 'package:seatrace/models/auction_model.dart';

class AuctionProvider extends ChangeNotifier {
  List<AuctionModel> _auctions = [];
  bool _isLoading = false;
  String? _error;
  
  List<AuctionModel> get auctions => _auctions;
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  // Obtenir les enchères actives pour un client
  Future<void> fetchActiveAuctions() async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      // Simulation d'un appel API
      await Future.delayed(const Duration(seconds: 1));
      
      // Données simulées
      _auctions = [
        AuctionModel(
          id: "auction1",
          lotId: "683d2ed832e8d0cdcbb75cd4",
          prixInitial: 100.0,
          dateDebut: DateTime.now().subtract(const Duration(hours: 1)),
          dateFin: DateTime.now().add(const Duration(hours: 2)),
          isActive: true,
          isTerminee: false,
          encheres: [
            BidModel(
              id: "bid1",
              clientId: "68347249a5e347d60aae73fc",
              clientNom: "Rayen Sfaxi",
              montant: 120.0,
              date: DateTime.now().subtract(const Duration(minutes: 30)),
            ),
          ],
        ),
      ];
      
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }
  
  // Créer une nouvelle enchère par un mareyeur
  Future<bool> createAuction(String lotId, double prixInitial, DateTime dateFin) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      // Simulation d'un appel API
      await Future.delayed(const Duration(seconds: 2));
      
      // Dans une vraie application, vous enverriez ces données à votre API
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }
  
  // Placer une enchère par un client
  Future<bool> placeBid(String auctionId, double montant) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      // Simulation d'un appel API
      await Future.delayed(const Duration(seconds: 1));
      
      // Dans une vraie application, vous enverriez ces données à votre API
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }
  
  // Clôturer une enchère par un mareyeur
  Future<bool> closeAuction(String auctionId, String gagnantId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();
    
    try {
      // Simulation d'un appel API
      await Future.delayed(const Duration(seconds: 1));
      
      // Dans une vraie application, vous enverriez ces données à votre API
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }
}
