import 'dart:io';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:image_picker/image_picker.dart';
import 'package:seatrace/screens/pecheur/fish_details_screen.dart';
import 'package:seatrace/main.dart';

class ScanFishScreen extends StatefulWidget {
  const ScanFishScreen({Key? key}) : super(key: key);

  @override
  State<ScanFishScreen> createState() => _ScanFishScreenState();
}

class _ScanFishScreenState extends State<ScanFishScreen> {
  CameraController? _controller;
  bool _isCameraInitialized = false;
  bool _isAnalyzing = false;
  File? _imageFile;
  
  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }
  
  Future<void> _initializeCamera() async {
    if (cameras.isEmpty) {
      return;
    }
    
    final camera = cameras.first;
    _controller = CameraController(
      camera,
      ResolutionPreset.high,
      enableAudio: false,
    );
    
    try {
      await _controller!.initialize();
      if (mounted) {
        setState(() {
          _isCameraInitialized = true;
        });
      }
    } catch (e) {
      print('Error initializing camera: $e');
    }
  }
  
  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }
  
  Future<void> _takePicture() async {
    if (_controller == null || !_isCameraInitialized) {
      return;
    }
    
    try {
      setState(() {
        _isAnalyzing = true;
      });
      
      final image = await _controller!.takePicture();
      final file = File(image.path);
      
      setState(() {
        _imageFile = file;
      });
      
      // Simuler l'analyse de l'IA
      await Future.delayed(const Duration(seconds: 2));
      
      if (mounted) {
        setState(() {
          _isAnalyzing = false;
        });
        
        _navigateToDetails();
      }
    } catch (e) {
      print('Error taking picture: $e');
      setState(() {
        _isAnalyzing = false;
      });
    }
  }
  
  Future<void> _pickImage() async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(source: ImageSource.gallery);
      
      if (pickedFile == null) {
        return;
      }
      
      setState(() {
        _imageFile = File(pickedFile.path);
        _isAnalyzing = true;
      });
      
      // Simuler l'analyse de l'IA
      await Future.delayed(const Duration(seconds: 2));
      
      if (mounted) {
        setState(() {
          _isAnalyzing = false;
        });
        
        _navigateToDetails();
      }
    } catch (e) {
      print('Error picking image: $e');
      setState(() {
        _isAnalyzing = false;
      });
    }
  }
  
  void _navigateToDetails() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (_) => FishDetailsScreen(
          imageFile: _imageFile!,
          fishSpecies: 'Rouget', // Simuler la détection de l'espèce
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Scanner un poisson'),
      ),
      body: Column(
        children: [
          Expanded(
            child: _imageFile != null
                ? Stack(
                    fit: StackFit.expand,
                    children: [
                      Image.file(
                        _imageFile!,
                        fit: BoxFit.cover,
                      ),
                      if (_isAnalyzing)
                        Container(
                          color: Colors.black54,
                          child: const Center(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                CircularProgressIndicator(
                                  color: Colors.white,
                                ),
                                SizedBox(height: 16),
                                Text(
                                  'Analyse en cours...',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 18,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                    ],
                  )
                : _isCameraInitialized
                    ? CameraPreview(_controller!)
                    : const Center(
                        child: CircularProgressIndicator(),
                      ),
          ),
          Container(
            padding: const EdgeInsets.all(20),
            color: Colors.black87,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  onPressed: _isAnalyzing ? null : _pickImage,
                  icon: const Icon(
                    Icons.photo_library,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
                IconButton(
                  onPressed: _isAnalyzing ? null : _takePicture,
                  icon: const Icon(
                    Icons.camera,
                    color: Colors.white,
                    size: 64,
                  ),
                ),
                const IconButton(
                  onPressed: null,
                  icon: Icon(
                    Icons.flip_camera_ios,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
