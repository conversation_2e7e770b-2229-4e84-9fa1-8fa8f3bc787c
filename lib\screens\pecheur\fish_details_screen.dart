import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:seatrace/providers/auth_provider.dart';
import 'package:seatrace/providers/lot_provider.dart';
import 'package:seatrace/screens/pecheur/pecheur_dashboard.dart';

class FishDetailsScreen extends StatefulWidget {
  final File imageFile;
  final String fishSpecies;

  const FishDetailsScreen({
    Key? key,
    required this.imageFile,
    required this.fishSpecies,
  }) : super(key: key);

  @override
  State<FishDetailsScreen> createState() => _FishDetailsScreenState();
}

class _FishDetailsScreenState extends State<FishDetailsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _quantiteController = TextEditingController();
  final _poidsController = TextEditingController();
  final _temperatureController = TextEditingController();
  final _lieuController = TextEditingController();
  final _descriptionController = TextEditingController();

  String _selectedVeterinaire = '';
  String _selectedMareyeur = '';

  // Données simulées pour les vétérinaires et mareyeurs
  final List<Map<String, String>> _veterinaires = [
    {'id': '68346fd4a5e347d60aae73cb', 'nom': 'Dr. Insaf Sfaxi'},
    {'id': '683470b6a5e347d60aae73d9', 'nom': 'Dr. Nourhene Ben Othman'},
    {'id': '6836decd688574cfc6913295', 'nom': 'Dr. Hichem Ksibi'},
  ];

  final List<Map<String, String>> _mareyeurs = [
    {'id': '68347159a5e347d60aae73e6', 'nom': 'Mareyeur Test'},
  ];

  @override
  void initState() {
    super.initState();
    if (_veterinaires.isNotEmpty) {
      _selectedVeterinaire = _veterinaires.first['id']!;
    }
    if (_mareyeurs.isNotEmpty) {
      _selectedMareyeur = _mareyeurs.first['id']!;
    }
  }

  @override
  void dispose() {
    _quantiteController.dispose();
    _poidsController.dispose();
    _temperatureController.dispose();
    _lieuController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _submitLot() async {
    if (_formKey.currentState!.validate()) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final lotProvider = Provider.of<LotProvider>(context, listen: false);

      final lotData = {
        'photo': widget.imageFile.path,
        'especeNom': widget.fishSpecies,
        'quantite': int.parse(_quantiteController.text),
        'poids': double.parse(_poidsController.text),
        'temperature': double.parse(_temperatureController.text),
        'lieu': _lieuController.text.trim(),
        'description': _descriptionController.text.trim(),
        'user': authProvider.currentUser!.id,
        'veterinaire': _selectedVeterinaire,
        'maryeur': _selectedMareyeur,
      };

      final success = await lotProvider.createLot(lotData);

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Lot créé avec succès!'),
            backgroundColor: Colors.green,
          ),
        );

        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (_) => const PecheurDashboard()),
          (route) => false,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final lotProvider = Provider.of<LotProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Détails du poisson'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Card(
                elevation: 4,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Column(
                  children: [
                    ClipRRect(
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(15),
                      ),
                      child: Image.file(
                        widget.imageFile,
                        height: 200,
                        width: double.infinity,
                        fit: BoxFit.cover,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.check_circle,
                            color: Colors.green,
                            size: 24,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Espèce détectée: ${widget.fishSpecies}',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              const Text(
                'Informations du lot',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _quantiteController,
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        labelText: 'Quantité',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Veuillez entrer la quantité';
                        }
                        if (int.tryParse(value) == null) {
                          return 'Veuillez entrer un nombre valide';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _poidsController,
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        labelText: 'Poids (kg)',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Veuillez entrer le poids';
                        }
                        if (double.tryParse(value) == null) {
                          return 'Veuillez entrer un nombre valide';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _temperatureController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'Température (°C)',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Veuillez entrer la température';
                  }
                  if (double.tryParse(value) == null) {
                    return 'Veuillez entrer un nombre valide';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _lieuController,
                decoration: InputDecoration(
                  labelText: 'Lieu de pêche',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Veuillez entrer le lieu de pêche';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                maxLines: 3,
                decoration: InputDecoration(
                  labelText: 'Description (optionnel)',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
              const SizedBox(height: 24),
              const Text(
                'Sélection des professionnels',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedVeterinaire.isNotEmpty
                    ? _selectedVeterinaire
                    : null,
                decoration: InputDecoration(
                  labelText: 'Vétérinaire',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                items: _veterinaires.map((vet) {
                  return DropdownMenuItem(
                    value: vet['id'],
                    child: Text(vet['nom']!),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedVeterinaire = value!;
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Veuillez sélectionner un vétérinaire';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedMareyeur.isNotEmpty ? _selectedMareyeur : null,
                decoration: InputDecoration(
                  labelText: 'Mareyeur',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                items: _mareyeurs.map((mareyeur) {
                  return DropdownMenuItem(
                    value: mareyeur['id'],
                    child: Text(mareyeur['nom']!),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedMareyeur = value!;
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Veuillez sélectionner un mareyeur';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 30),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: lotProvider.isLoading ? null : _submitLot,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  child: lotProvider.isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : const Text(
                          'Soumettre le lot',
                          style: TextStyle(fontSize: 16),
                        ),
                ),
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }
}
