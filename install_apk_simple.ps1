# Script d'installation simple pour SeaTrace APK
# Transfert automatique vers Samsung Galaxy S23 Ultra

Write-Host "=== Installation SeaTrace avec Images Réelles ===" -ForegroundColor Green
Write-Host ""

# Chemin vers l'APK
$apkPath = "build\app\outputs\flutter-apk\app-release.apk"

# Vérifier que l'APK existe
if (-not (Test-Path $apkPath)) {
    Write-Host "❌ APK non trouvé: $apkPath" -ForegroundColor Red
    Write-Host "Compilez d'abord l'application avec: flutter build apk --release" -ForegroundColor Yellow
    exit 1
}

# Informations sur l'APK
$apkSize = (Get-Item $apkPath).Length
$apkSizeMB = [math]::Round($apkSize / 1MB, 1)
Write-Host "📱 APK trouvé: $apkPath ($apkSizeMB MB)" -ForegroundColor Cyan
Write-Host ""

# Vérifier la connexion ADB
Write-Host "🔍 Vérification de la connexion ADB..." -ForegroundColor Yellow

try {
    $devices = adb devices 2>&1
    if ($devices -match "SM-S911U1") {
        Write-Host "✅ Samsung Galaxy S23 Ultra détecté" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Téléphone non détecté ou ADB non configuré" -ForegroundColor Yellow
        Write-Host "Assurez-vous que:" -ForegroundColor White
        Write-Host "1. Le débogage USB est activé" -ForegroundColor White
        Write-Host "2. Le téléphone est connecté en USB" -ForegroundColor White
        Write-Host "3. ADB est installé et dans le PATH" -ForegroundColor White
        Write-Host ""
        
        $continue = Read-Host "Continuer quand même? (o/n)"
        if ($continue -ne "o" -and $continue -ne "O") {
            exit 0
        }
    }
} catch {
    Write-Host "⚠️ ADB non trouvé dans le PATH" -ForegroundColor Yellow
    Write-Host "Installation manuelle requise" -ForegroundColor White
}

Write-Host ""
Write-Host "🚀 Installation de l'APK..." -ForegroundColor Yellow

try {
    # Installation de l'APK
    $result = adb install -r $apkPath 2>&1
    
    if ($result -match "Success") {
        Write-Host ""
        Write-Host "🎉 Installation réussie !" -ForegroundColor Green
        Write-Host ""
        Write-Host "📱 SeaTrace avec images réelles installé sur Samsung Galaxy S23 Ultra" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "🎣 Pour tester l'historique avec images:" -ForegroundColor Yellow
        Write-Host "1. Ouvrez l'application SeaTrace" -ForegroundColor White
        Write-Host "2. Connectez-vous comme pêcheur:" -ForegroundColor White
        Write-Host "   Email: <EMAIL>" -ForegroundColor White
        Write-Host "   Mot de passe: Zouhaier1*" -ForegroundColor White
        Write-Host "3. Cliquez sur l'icône historique ⏰" -ForegroundColor White
        Write-Host "4. Admirez les 6 vraies images de poissons !" -ForegroundColor White
        Write-Host ""
        Write-Host "🐟 Images incluses: Daurade, Thon, Calamar, Espadon, Scorpaena, Crevettes" -ForegroundColor Cyan
        
    } else {
        Write-Host ""
        Write-Host "❌ Erreur lors de l'installation:" -ForegroundColor Red
        Write-Host $result -ForegroundColor Red
        Write-Host ""
        Write-Host "💡 Solutions possibles:" -ForegroundColor Yellow
        Write-Host "1. Activez 'Sources inconnues' dans les paramètres Android" -ForegroundColor White
        Write-Host "2. Désinstallez l'ancienne version de SeaTrace" -ForegroundColor White
        Write-Host "3. Redémarrez le téléphone et réessayez" -ForegroundColor White
    }
    
} catch {
    Write-Host ""
    Write-Host "❌ Erreur lors de l'installation" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Write-Host ""
    Write-Host "📋 Installation manuelle:" -ForegroundColor Yellow
    Write-Host "1. Copiez le fichier $apkPath sur votre téléphone" -ForegroundColor White
    Write-Host "2. Ouvrez le fichier APK sur le téléphone" -ForegroundColor White
    Write-Host "3. Suivez les instructions d'installation" -ForegroundColor White
}

Write-Host ""
Write-Host "✨ Script terminé" -ForegroundColor Green
Write-Host ""
Write-Host "Appuyez sur une touche pour continuer..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
