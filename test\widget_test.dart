// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';

import 'package:seatrace/main.dart';

void main() {
  testWidgets('SeaTrace app smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const SeaTraceApp());

    // Verify that the app loads and shows the splash screen
    expect(find.text('SeaTrace'), findsOneWidget);
    expect(find.text('Traçabilité de pêche'), findsOneWidget);

    // Wait for splash screen to complete
    await tester.pumpAndSettle(const Duration(seconds: 4));

    // Verify that we navigate to login screen
    expect(find.text('Connexion'), findsOneWidget);
  });
}
