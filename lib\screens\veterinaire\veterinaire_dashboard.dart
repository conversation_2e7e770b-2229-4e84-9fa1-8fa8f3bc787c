import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:seatrace/providers/auth_provider.dart';
import 'package:seatrace/providers/lot_provider.dart';
import 'package:seatrace/providers/history_provider.dart';
import 'package:seatrace/screens/auth/login_screen.dart';
import 'package:seatrace/screens/history/history_screen.dart';
import 'package:seatrace/widgets/lot_validation_card.dart';
import 'package:seatrace/widgets/profile_avatar.dart';

class VeterinaireDashboard extends StatefulWidget {
  const VeterinaireDashboard({Key? key}) : super(key: key);

  @override
  State<VeterinaireDashboard> createState() => _VeterinaireDashboardState();
}

class _VeterinaireDashboardState extends State<VeterinaireDashboard> {
  @override
  void initState() {
    super.initState();
    _loadPendingLots();
  }

  Future<void> _loadPendingLots() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final lotProvider = Provider.of<LotProvider>(context, listen: false);

    if (authProvider.isLoggedIn) {
      await lotProvider
          .fetchVeterinairePendingLots(authProvider.currentUser!.id);
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final lotProvider = Provider.of<LotProvider>(context);

    if (!authProvider.isLoggedIn) {
      return const LoginScreen();
    }

    final user = authProvider.currentUser!;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Validation des lots'),
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(builder: (_) => const HistoryScreen()),
              );
            },
            tooltip: 'Historique des validations',
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () {
              authProvider.logout();
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(builder: (_) => const LoginScreen()),
              );
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadPendingLots,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Card(
                  elevation: 4,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            ProfileAvatar(
                              user: user,
                              radius: 30,
                            ),
                            const SizedBox(width: 16),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '${user.prenom} ${user.nom}',
                                  style: const TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'Vétérinaire',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        const Divider(height: 30),
                        _buildInfoRow('Cabinet', user.port ?? 'Non spécifié'),
                        _buildInfoRow(
                            'Matricule', user.matricule ?? 'Non spécifié'),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Lots en attente',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${lotProvider.lots.length} lots',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                if (lotProvider.isLoading)
                  const Center(
                    child: CircularProgressIndicator(),
                  )
                else if (lotProvider.lots.isEmpty)
                  Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const SizedBox(height: 40),
                        Icon(
                          Icons.check_circle_outline,
                          size: 80,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Aucun lot en attente',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Tous les lots ont été traités',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey[500],
                          ),
                        ),
                      ],
                    ),
                  )
                else
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: lotProvider.lots.length,
                    itemBuilder: (context, index) {
                      final lot = lotProvider.lots[index];
                      return LotValidationCard(
                        lot: lot,
                        onValidate: (isApproved) async {
                          final historyProvider = Provider.of<HistoryProvider>(
                              context,
                              listen: false);
                          final authProvider =
                              Provider.of<AuthProvider>(context, listen: false);
                          final success = await lotProvider.validateLot(
                              lot.id,
                              isApproved,
                              historyProvider,
                              authProvider.currentUser!.id);
                          if (success && mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  isApproved
                                      ? 'Lot validé avec succès!'
                                      : 'Lot refusé',
                                ),
                                backgroundColor:
                                    isApproved ? Colors.green : Colors.red,
                              ),
                            );
                            _loadPendingLots();
                          }
                        },
                      );
                    },
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
