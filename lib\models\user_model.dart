class UserModel {
  final String id;
  final String email;
  final String role;
  final String nom;
  final String prenom;
  final String telephone;
  final bool isValidated;
  final bool isBlocked;
  
  // Champs spécifiques pour pêcheur
  final String? cin;
  final String? matricule;
  final String? bateau;
  final String? port;
  final String? capacite;
  
  UserModel({
    required this.id,
    required this.email,
    required this.role,
    required this.nom,
    required this.prenom,
    required this.telephone,
    required this.isValidated,
    required this.isBlocked,
    this.cin,
    this.matricule,
    this.bateau,
    this.port,
    this.capacite,
  });
  
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['_id'] is Map ? json['_id']['\$oid'] : json['_id'],
      email: json['email'],
      role: json['roles'],
      nom: json['nom'],
      prenom: json['prenom'],
      telephone: json['telephone'],
      isValidated: json['isValidated'] ?? false,
      isBlocked: json['isBlocked'] ?? false,
      cin: json['cin'],
      matricule: json['matricule'],
      bateau: json['bateau'],
      port: json['port'],
      capacite: json['capacite'],
    );
  }
  
  String get fullName => '$prenom $nom';
}
