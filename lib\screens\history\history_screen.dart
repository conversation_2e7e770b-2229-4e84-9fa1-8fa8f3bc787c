import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:seatrace/providers/auth_provider.dart';
import 'package:seatrace/providers/history_provider.dart';
import 'package:seatrace/models/history_model.dart';
import 'package:seatrace/widgets/history_item_card.dart';

class HistoryScreen extends StatefulWidget {
  const HistoryScreen({Key? key}) : super(key: key);

  @override
  State<HistoryScreen> createState() => _HistoryScreenState();
}

class _HistoryScreenState extends State<HistoryScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadHistory();
    });
  }

  Future<void> _loadHistory() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final historyProvider = Provider.of<HistoryProvider>(context, listen: false);
    final user = authProvider.currentUser!;

    // Charger l'historique selon le rôle de l'utilisateur
    switch (user.role) {
      case 'ROLE_PECHEUR':
        await historyProvider.loadScannedLotsHistory(user.id);
        break;
      case 'ROLE_VETERINAIRE':
        await historyProvider.loadValidatedLotsHistory(user.id);
        break;
      case 'ROLE_MAREYEUR':
        await historyProvider.loadCreatedAuctionsHistory(user.id);
        break;
      case 'ROLE_CLIENT':
        await historyProvider.loadBidHistory(user.id);
        break;
    }
  }

  String _getHistoryTitle(String role) {
    switch (role) {
      case 'ROLE_PECHEUR':
        return 'Historique des lots scannés';
      case 'ROLE_VETERINAIRE':
        return 'Historique des validations';
      case 'ROLE_MAREYEUR':
        return 'Historique des enchères lancées';
      case 'ROLE_CLIENT':
        return 'Historique des enchères participées';
      default:
        return 'Historique';
    }
  }

  String _getEmptyMessage(String role) {
    switch (role) {
      case 'ROLE_PECHEUR':
        return 'Aucun lot scanné pour le moment';
      case 'ROLE_VETERINAIRE':
        return 'Aucune validation effectuée pour le moment';
      case 'ROLE_MAREYEUR':
        return 'Aucune enchère lancée pour le moment';
      case 'ROLE_CLIENT':
        return 'Aucune participation aux enchères pour le moment';
      default:
        return 'Aucun historique disponible';
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final historyProvider = Provider.of<HistoryProvider>(context);
    final user = authProvider.currentUser!;
    
    final userHistory = historyProvider.getHistoryForUser(user.id);

    return Scaffold(
      appBar: AppBar(
        title: Text(_getHistoryTitle(user.role)),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: RefreshIndicator(
        onRefresh: _loadHistory,
        child: historyProvider.isLoading
            ? const Center(
                child: CircularProgressIndicator(),
              )
            : historyProvider.error != null
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Erreur lors du chargement',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          historyProvider.error!,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[500],
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _loadHistory,
                          child: const Text('Réessayer'),
                        ),
                      ],
                    ),
                  )
                : userHistory.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              _getEmptyIcon(user.role),
                              size: 64,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              _getEmptyMessage(user.role),
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.grey[600],
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: userHistory.length,
                        itemBuilder: (context, index) {
                          final historyItem = userHistory[index];
                          return HistoryItemCard(
                            historyItem: historyItem,
                            userRole: user.role,
                          );
                        },
                      ),
      ),
    );
  }

  IconData _getEmptyIcon(String role) {
    switch (role) {
      case 'ROLE_PECHEUR':
        return Icons.camera_alt_outlined;
      case 'ROLE_VETERINAIRE':
        return Icons.verified_outlined;
      case 'ROLE_MAREYEUR':
        return Icons.gavel_outlined;
      case 'ROLE_CLIENT':
        return Icons.shopping_cart_outlined;
      default:
        return Icons.history;
    }
  }
}
