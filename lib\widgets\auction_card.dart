import 'package:flutter/material.dart';
import 'package:seatrace/models/auction_model.dart';

class AuctionCard extends StatefulWidget {
  final AuctionModel auction;
  final Function(double) onPlaceBid;

  const AuctionCard({
    Key? key,
    required this.auction,
    required this.onPlaceBid,
  }) : super(key: key);

  @override
  State<AuctionCard> createState() => _AuctionCardState();
}

class _AuctionCardState extends State<AuctionCard> {
  final _bidController = TextEditingController();

  @override
  void dispose() {
    _bidController.dispose();
    super.dispose();
  }

  void _showBidDialog() {
    final currentBid = widget.auction.encheres.isNotEmpty
        ? widget.auction.encheres.last.montant
        : widget.auction.prixInitial;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Placer une enchère'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Enchère actuelle: ${currentBid.toStringAsFixed(2)} DT',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _bidController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Votre enchère (DT)',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              if (_bidController.text.isNotEmpty) {
                final bid = double.parse(_bidController.text);
                if (bid > currentBid) {
                  widget.onPlaceBid(bid);
                  Navigator.of(context).pop();
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Votre enchère doit être supérieure à l\'enchère actuelle'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: const Text('Enchérir'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentBid = widget.auction.encheres.isNotEmpty
        ? widget.auction.encheres.last.montant
        : widget.auction.prixInitial;

    final timeRemaining = widget.auction.dateFin.difference(DateTime.now());
    final isExpired = timeRemaining.isNegative;

    return Card(
      elevation: 4,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Lot ${widget.auction.lotId.substring(0, 8)}...',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: isExpired ? Colors.red : Colors.green,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    isExpired ? 'Terminée' : 'Active',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: Colors.grey[300],
                  ),
                  child: const Icon(
                    Icons.image,
                    size: 40,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Prix initial: ${widget.auction.prixInitial.toStringAsFixed(2)} DT',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Enchère actuelle: ${currentBid.toStringAsFixed(2)} DT',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        isExpired
                            ? 'Enchère terminée'
                            : 'Temps restant: ${_formatDuration(timeRemaining)}',
                        style: TextStyle(
                          fontSize: 14,
                          color: isExpired ? Colors.red : Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (widget.auction.encheres.isNotEmpty) ...[
              const SizedBox(height: 12),
              const Text(
                'Dernières enchères:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              ...widget.auction.encheres.take(3).map((bid) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),
                    child: Text(
                      '${bid.clientNom}: ${bid.montant.toStringAsFixed(2)} DT',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  )),
            ],
            const SizedBox(height: 16),
            if (!isExpired)
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _showBidDialog,
                  icon: const Icon(Icons.gavel),
                  label: const Text('Enchérir'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.secondary,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    if (duration.inDays > 0) {
      return '${duration.inDays}j ${duration.inHours % 24}h';
    } else if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes % 60}m';
    } else {
      return '${duration.inMinutes}m';
    }
  }
}
