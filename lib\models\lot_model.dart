class LotModel {
  final String id;
  final String identifiant;
  final String photo;
  final String especeNom;
  final int quantite;
  final double poids;
  final double temperature;
  final String lieu;
  final String? description;
  final DateTime dateSoumission;
  final bool isValidated;
  final bool vendu;
  final bool isAuctionActive;
  final bool online;
  final String typeEnchere;
  final String userId;
  final String veterinaireId;
  final String maryeurId;
  final bool status;
  
  LotModel({
    required this.id,
    required this.identifiant,
    required this.photo,
    required this.especeNom,
    required this.quantite,
    required this.poids,
    required this.temperature,
    required this.lieu,
    this.description,
    required this.dateSoumission,
    required this.isValidated,
    required this.vendu,
    required this.isAuctionActive,
    required this.online,
    required this.typeEnchere,
    required this.userId,
    required this.veterinaireId,
    required this.maryeurId,
    required this.status,
  });
  
  factory LotModel.fromJson(Map<String, dynamic> json) {
    return LotModel(
      id: json['_id'] is Map ? json['_id']['\$oid'] : json['_id'],
      identifiant: json['identifiant'],
      photo: json['photo'],
      especeNom: json['especeNom'],
      quantite: json['quantite'],
      poids: json['poids'].toDouble(),
      temperature: json['temperature'].toDouble(),
      lieu: json['lieu'],
      description: json['description'],
      dateSoumission: json['dateSoumission'] is Map 
          ? DateTime.parse(json['dateSoumission']['\$date'])
          : DateTime.parse(json['dateSoumission']),
      isValidated: json['isValidated'],
      vendu: json['vendu'],
      isAuctionActive: json['isAuctionActive'],
      online: json['online'],
      typeEnchere: json['typeEnchere'],
      userId: json['user'] is Map ? json['user']['\$oid'] : json['user'],
      veterinaireId: json['veterinaire'] is Map ? json['veterinaire']['\$oid'] : json['veterinaire'],
      maryeurId: json['maryeur'] is Map ? json['maryeur']['\$oid'] : json['maryeur'],
      status: json['status'],
    );
  }
}
