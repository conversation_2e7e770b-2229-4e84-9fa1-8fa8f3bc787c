import 'package:flutter/material.dart';
import 'package:seatrace/models/history_model.dart';
import 'package:intl/intl.dart';

class HistoryItemCard extends StatelessWidget {
  final HistoryItemModel historyItem;
  final String userRole;

  const HistoryItemCard({
    Key? key,
    required this.historyItem,
    required this.userRole,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                _buildIcon(),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        historyItem.title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        historyItem.description,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                _buildStatusChip(),
              ],
            ),
            const SizedBox(height: 12),
            _buildDetails(),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  DateFormat('dd/MM/yyyy à HH:mm').format(historyItem.date),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[500],
                  ),
                ),
                _buildActionButton(context),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIcon() {
    IconData iconData;
    Color iconColor;

    switch (historyItem.type) {
      case 'lot_scanned':
        iconData = Icons.camera_alt;
        iconColor = Colors.blue;
        break;
      case 'lot_validated':
        iconData = historyItem.data['isApproved'] == true
            ? Icons.check_circle
            : Icons.cancel;
        iconColor =
            historyItem.data['isApproved'] == true ? Colors.green : Colors.red;
        break;
      case 'auction_created':
        iconData = Icons.gavel;
        iconColor = Colors.orange;
        break;
      case 'bid_placed':
        iconData = Icons.shopping_cart;
        iconColor = _getBidColor();
        break;
      default:
        iconData = Icons.history;
        iconColor = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: iconColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        iconData,
        color: iconColor,
        size: 24,
      ),
    );
  }

  Widget _buildStatusChip() {
    Color chipColor;
    String statusText;

    switch (historyItem.status) {
      case 'completed':
      case 'approved':
        chipColor = Colors.green;
        statusText = 'Terminé';
        break;
      case 'active':
        chipColor = Colors.blue;
        statusText = 'En cours';
        break;
      case 'pending':
        chipColor = Colors.orange;
        statusText = 'En attente';
        break;
      case 'rejected':
      case 'failed':
        chipColor = Colors.red;
        statusText = 'Refusé';
        break;
      case 'won':
        chipColor = Colors.green;
        statusText = 'Gagné';
        break;
      case 'lost':
        chipColor = Colors.red;
        statusText = 'Perdu';
        break;
      default:
        chipColor = Colors.grey;
        statusText = historyItem.status;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: chipColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: chipColor,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildDetails() {
    switch (historyItem.type) {
      case 'lot_scanned':
        return _buildScannedLotDetails();
      case 'lot_validated':
        return _buildValidatedLotDetails();
      case 'auction_created':
        return _buildAuctionDetails();
      case 'bid_placed':
        return _buildBidDetails();
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildScannedLotDetails() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Image du lot si disponible
          if (historyItem.data['imagePath'] != null)
            Container(
              margin: const EdgeInsets.only(bottom: 12),
              height: 120,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: _buildLotImage(),
              ),
            ),
          _buildDetailRow('Espèce', historyItem.data['especeNom']),
          _buildDetailRow('Poids', '${historyItem.data['poids']} kg'),
          _buildDetailRow('Lieu', historyItem.data['lieu']),
        ],
      ),
    );
  }

  Widget _buildValidatedLotDetails() {
    final isApproved = historyItem.data['isApproved'] == true;
    final borderColor = isApproved ? Colors.green : Colors.red;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: borderColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Image du lot si disponible
          if (historyItem.data['imagePath'] != null)
            Container(
              margin: const EdgeInsets.only(bottom: 12),
              height: 120,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: borderColor.withValues(alpha: 0.3)),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: _buildLotImage(),
              ),
            ),
          _buildDetailRow('Espèce', historyItem.data['especeNom']),
          _buildDetailRow('Décision', isApproved ? 'Approuvé' : 'Refusé'),
          if (historyItem.data['comment'] != null)
            _buildDetailRow('Commentaire', historyItem.data['comment']),
        ],
      ),
    );
  }

  Widget _buildAuctionDetails() {
    final isCompleted = historyItem.status == 'completed';
    final isActive = historyItem.status == 'active';
    final borderColor =
        isCompleted ? Colors.green : (isActive ? Colors.orange : Colors.grey);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: borderColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: borderColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          // Image du lot si disponible
          if (historyItem.data['imagePath'] != null)
            Container(
              margin: const EdgeInsets.only(bottom: 12),
              height: 120,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: borderColor.withValues(alpha: 0.3)),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: _buildLotImage(),
              ),
            ),
          _buildDetailRow('Espèce', historyItem.data['especeNom']),
          _buildDetailRow(
              'Prix initial', '${historyItem.data['prixInitial']} €'),

          // Afficher le prix final si l'enchère est terminée ou en cours
          if (historyItem.data['prixFinal'] != null)
            _buildDetailRow(isCompleted ? 'Prix final' : 'Enchère actuelle',
                '${historyItem.data['prixFinal']} €'),

          // Afficher le client gagnant si disponible
          if (historyItem.data['clientGagnantNom'] != null)
            _buildDetailRow(isCompleted ? 'Gagnant' : 'En tête',
                historyItem.data['clientGagnantNom']),

          _buildDetailRow(
              isCompleted ? 'Terminée le' : 'Se termine le',
              DateFormat('dd/MM/yyyy à HH:mm')
                  .format(DateTime.parse(historyItem.data['dateFin']))),
        ],
      ),
    );
  }

  Widget _buildBidDetails() {
    final isWinning = historyItem.data['isWinning'] == true;
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _getBidColor().withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          _buildDetailRow('Espèce', historyItem.data['especeNom']),
          _buildDetailRow('Montant', '${historyItem.data['montant']} €'),
          _buildDetailRow(
              'Position', isWinning ? 'Enchère gagnante' : 'Enchère perdante'),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    // Pour les commentaires longs, utiliser un layout différent
    if (label == 'Commentaire' && value.length > 30) {
      return _buildCommentRow(label, value);
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 13,
              color: Colors.grey[600],
            ),
          ),
          Flexible(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.right,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommentRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 13,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w400,
                height: 1.3,
              ),
              softWrap: true,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(BuildContext context) {
    if (historyItem.type == 'auction_created' &&
        historyItem.status == 'active') {
      return TextButton(
        onPressed: () {
          // Navigation vers les détails de l'enchère
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Voir les détails de l\'enchère')),
          );
        },
        child: const Text('Voir détails'),
      );
    } else if (historyItem.type == 'bid_placed' &&
        historyItem.status == 'active') {
      return TextButton(
        onPressed: () {
          // Navigation vers l'enchère en cours
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Voir l\'enchère en cours')),
          );
        },
        child: const Text('Voir enchère'),
      );
    }
    return const SizedBox.shrink();
  }

  Color _getBidColor() {
    switch (historyItem.status) {
      case 'won':
        return Colors.green;
      case 'lost':
        return Colors.red;
      case 'active':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  Widget _buildLotImage() {
    final especeNom = historyItem.data['especeNom'] as String? ?? '';
    final imagePath = historyItem.data['imagePath'] as String?;

    // Essayer de charger une vraie image d'abord
    if (imagePath != null) {
      return Image.asset(
        imagePath,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          // Si l'image ne peut pas être chargée, utiliser le design coloré
          return _buildColoredSpeciesImage(especeNom);
        },
      );
    }

    // Sinon, utiliser le design coloré par défaut
    return _buildColoredSpeciesImage(especeNom);
  }

  Widget _buildColoredSpeciesImage(String especeNom) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: _getSpeciesColors(especeNom),
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getSpeciesIcon(especeNom),
            color: Colors.white,
            size: 50,
            shadows: const [
              Shadow(
                offset: Offset(1, 1),
                blurRadius: 3,
                color: Colors.black26,
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            especeNom,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.white,
              shadows: [
                Shadow(
                  offset: Offset(1, 1),
                  blurRadius: 3,
                  color: Colors.black26,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<Color> _getSpeciesColors(String? species) {
    switch (species?.toLowerCase()) {
      case 'daurade':
        return [const Color(0xFFFFD700), const Color(0xFFFF8C00)]; // Doré
      case 'thon':
        return [const Color(0xFF4169E1), const Color(0xFF191970)]; // Bleu océan
      case 'calamar':
        return [const Color(0xFF8A2BE2), const Color(0xFF4B0082)]; // Violet
      case 'espadon':
        return [const Color(0xFF2E8B57), const Color(0xFF006400)]; // Vert mer
      case 'scorpaena':
        return [const Color(0xFFDC143C), const Color(0xFF8B0000)]; // Rouge
      case 'crevette':
        return [const Color(0xFFFF69B4), const Color(0xFFFF1493)]; // Rose
      default:
        return [const Color(0xFF87CEEB), const Color(0xFF4682B4)]; // Bleu ciel
    }
  }

  IconData _getSpeciesIcon(String? species) {
    switch (species?.toLowerCase()) {
      case 'daurade':
        return Icons.set_meal; // Poisson doré
      case 'thon':
        return Icons.dining; // Gros poisson
      case 'calamar':
        return Icons.waves; // Tentacules
      case 'espadon':
        return Icons.navigation; // Forme d'épée
      case 'scorpaena':
        return Icons.warning; // Épines dangereuses
      case 'crevette':
        return Icons.cruelty_free; // Petite créature
      default:
        return Icons.set_meal;
    }
  }
}
