import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:seatrace/providers/auth_provider.dart';
import 'package:seatrace/screens/auth/login_screen.dart';
import 'package:seatrace/screens/pecheur/pecheur_dashboard.dart';
import 'package:seatrace/screens/veterinaire/veterinaire_dashboard.dart';
import 'package:seatrace/screens/mareyeur/mareyeur_dashboard.dart';
import 'package:seatrace/screens/client/client_dashboard.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    
    if (!authProvider.isLoggedIn) {
      return const LoginScreen();
    }
    
    final user = authProvider.currentUser!;
    
    switch (user.role) {
      case 'ROLE_PECHEUR':
        return const PecheurDashboard();
      case 'ROLE_VETERINAIRE':
        return const VeterinaireDashboard();
      case 'ROLE_MAREYEUR':
        return const MareyeurDashboard();
      case 'ROLE_CLIENT':
        return const ClientDashboard();
      default:
        return Scaffold(
          appBar: AppBar(
            title: const Text('SeaTrace'),
            actions: [
              IconButton(
                icon: const Icon(Icons.logout),
                onPressed: () {
                  authProvider.logout();
                  Navigator.of(context).pushReplacement(
                    MaterialPageRoute(builder: (_) => const LoginScreen()),
                  );
                },
              ),
            ],
          ),
          body: const Center(
            child: Text('Rôle non reconnu'),
          ),
        );
    }
  }
}
