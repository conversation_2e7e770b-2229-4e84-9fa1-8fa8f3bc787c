import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:seatrace/providers/auth_provider.dart';
import 'package:seatrace/providers/lot_provider.dart';
import 'package:seatrace/providers/auction_provider.dart';
import 'package:seatrace/screens/auth/login_screen.dart';
import 'package:seatrace/widgets/lot_auction_card.dart';

class MareyeurDashboard extends StatefulWidget {
  const MareyeurDashboard({Key? key}) : super(key: key);

  @override
  State<MareyeurDashboard> createState() => _MareyeurDashboardState();
}

class _MareyeurDashboardState extends State<MareyeurDashboard> {
  @override
  void initState() {
    super.initState();
    _loadValidatedLots();
  }

  Future<void> _loadValidatedLots() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final lotProvider = Provider.of<LotProvider>(context, listen: false);
    
    if (authProvider.isLoggedIn) {
      await lotProvider.fetchMareyeurValidatedLots(authProvider.currentUser!.id);
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final lotProvider = Provider.of<LotProvider>(context);
    
    if (!authProvider.isLoggedIn) {
      return const LoginScreen();
    }
    
    final user = authProvider.currentUser!;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Gestion des enchères'),
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () {
              authProvider.logout();
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(builder: (_) => const LoginScreen()),
              );
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadValidatedLots,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Card(
                  elevation: 4,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            CircleAvatar(
                              radius: 30,
                              backgroundColor: Theme.of(context).primaryColor,
                              child: Text(
                                user.prenom[0] + user.nom[0],
                                style: const TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '${user.prenom} ${user.nom}',
                                  style: const TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'Mareyeur',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Lots validés',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${lotProvider.lots.length} lots',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                if (lotProvider.isLoading)
                  const Center(
                    child: CircularProgressIndicator(),
                  )
                else if (lotProvider.lots.isEmpty)
                  Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const SizedBox(height: 40),
                        Icon(
                          Icons.gavel,
                          size: 80,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Aucun lot validé',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'En attente de validation vétérinaire',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey[500],
                          ),
                        ),
                      ],
                    ),
                  )
                else
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: lotProvider.lots.length,
                    itemBuilder: (context, index) {
                      final lot = lotProvider.lots[index];
                      return LotAuctionCard(
                        lot: lot,
                        onStartAuction: (prixInitial, dateFin) async {
                          final auctionProvider = Provider.of<AuctionProvider>(context, listen: false);
                          final success = await auctionProvider.createAuction(
                            lot.id,
                            prixInitial,
                            dateFin,
                          );
                          if (success) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('Enchère lancée avec succès!'),
                                backgroundColor: Colors.green,
                              ),
                            );
                            _loadValidatedLots();
                          }
                        },
                      );
                    },
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
