class AuctionModel {
  final String id;
  final String lotId;
  final double prixInitial;
  final double? prixFinal;
  final DateTime dateDebut;
  final DateTime dateFin;
  final bool isActive;
  final bool isTerminee;
  final String? gagnantId;
  final List<BidModel> encheres;
  
  AuctionModel({
    required this.id,
    required this.lotId,
    required this.prixInitial,
    this.prixFinal,
    required this.dateDebut,
    required this.dateFin,
    required this.isActive,
    required this.isTerminee,
    this.gagnantId,
    required this.encheres,
  });
  
  factory AuctionModel.fromJson(Map<String, dynamic> json) {
    List<BidModel> bids = [];
    if (json['encheres'] != null) {
      bids = (json['encheres'] as List).map((e) => BidModel.fromJson(e)).toList();
    }
    
    return AuctionModel(
      id: json['_id'] is Map ? json['_id']['\$oid'] : json['_id'],
      lotId: json['lot'] is Map ? json['lot']['\$oid'] : json['lot'],
      prixInitial: json['prixInitial'].toDouble(),
      prixFinal: json['prixFinal']?.toDouble(),
      dateDebut: json['dateDebut'] is Map 
          ? DateTime.parse(json['dateDebut']['\$date'])
          : DateTime.parse(json['dateDebut']),
      dateFin: json['dateFin'] is Map 
          ? DateTime.parse(json['dateFin']['\$date'])
          : DateTime.parse(json['dateFin']),
      isActive: json['isActive'],
      isTerminee: json['isTerminee'],
      gagnantId: json['gagnant'] is Map ? json['gagnant']['\$oid'] : json['gagnant'],
      encheres: bids,
    );
  }
}

class BidModel {
  final String id;
  final String clientId;
  final String clientNom;
  final double montant;
  final DateTime date;
  
  BidModel({
    required this.id,
    required this.clientId,
    required this.clientNom,
    required this.montant,
    required this.date,
  });
  
  factory BidModel.fromJson(Map<String, dynamic> json) {
    return BidModel(
      id: json['_id'] is Map ? json['_id']['\$oid'] : json['_id'],
      clientId: json['client'] is Map ? json['client']['\$oid'] : json['client'],
      clientNom: json['clientNom'],
      montant: json['montant'].toDouble(),
      date: json['date'] is Map 
          ? DateTime.parse(json['date']['\$date'])
          : DateTime.parse(json['date']),
    );
  }
}
