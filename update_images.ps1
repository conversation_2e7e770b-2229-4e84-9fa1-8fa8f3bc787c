# Script de mise à jour des images de poissons pour SeaTrace
# Vérifie la présence des images et recompile l'application si nécessaire

Write-Host "=== Mise à jour des images de poissons SeaTrace ===" -ForegroundColor Green
Write-Host ""

# Liste des images requises
$images = @("daurade.jpg", "thon.jpg", "calamar.jpg", "espadon.jpg", "scorpaena.jpg", "crevette.jpg")
$lotPath = "assets\lot"

# Vérifier que le dossier existe
if (-not (Test-Path $lotPath)) {
    Write-Host "❌ Dossier $lotPath non trouvé" -ForegroundColor Red
    Write-Host "Assurez-vous d'être dans le répertoire racine du projet SeaTrace" -ForegroundColor Yellow
    exit 1
}

Write-Host "📁 Vérification des images dans $lotPath..." -ForegroundColor Cyan
Write-Host ""

# Vérifier la présence de chaque image
$foundImages = 0
$missingImages = @()

foreach ($image in $images) {
    $imagePath = Join-Path $lotPath $image
    if (Test-Path $imagePath) {
        $fileSize = (Get-Item $imagePath).Length
        $fileSizeKB = [math]::Round($fileSize / 1KB, 1)
        Write-Host "✓ $image trouvée ($fileSizeKB KB)" -ForegroundColor Green
        $foundImages++
    } else {
        Write-Host "❌ $image manquante" -ForegroundColor Red
        $missingImages += $image
    }
}

Write-Host ""
Write-Host "📊 Résumé: $foundImages/$($images.Count) images trouvées" -ForegroundColor Cyan

# Si des images manquent, afficher les instructions
if ($missingImages.Count -gt 0) {
    Write-Host ""
    Write-Host "⚠️ Images manquantes:" -ForegroundColor Yellow
    foreach ($missing in $missingImages) {
        Write-Host "   - $missing" -ForegroundColor Red
    }
    
    Write-Host ""
    Write-Host "📋 Instructions pour ajouter les images:" -ForegroundColor Yellow
    Write-Host "1. Préparez vos images (300x200 pixels, format JPG/PNG)" -ForegroundColor White
    Write-Host "2. Nommez-les exactement comme indiqué ci-dessus" -ForegroundColor White
    Write-Host "3. Copiez-les dans le dossier $lotPath" -ForegroundColor White
    Write-Host "4. Relancez ce script pour compiler l'application" -ForegroundColor White
    Write-Host ""
    Write-Host "💡 Consultez GUIDE_AJOUT_IMAGES.md pour plus de détails" -ForegroundColor Cyan
    
    # Proposer d'ouvrir le dossier
    $openFolder = Read-Host "Voulez-vous ouvrir le dossier assets\lot? (o/n)"
    if ($openFolder -eq "o" -or $openFolder -eq "O") {
        Start-Process explorer.exe -ArgumentList (Resolve-Path $lotPath)
    }
    
    exit 0
}

# Toutes les images sont présentes, proposer la compilation
Write-Host ""
Write-Host "🎉 Toutes les images sont présentes !" -ForegroundColor Green
Write-Host ""

$compile = Read-Host "Voulez-vous compiler l'application maintenant? (o/n)"
if ($compile -eq "o" -or $compile -eq "O") {
    Write-Host ""
    Write-Host "🧹 Nettoyage du projet..." -ForegroundColor Yellow
    
    try {
        flutter clean | Out-Null
        Write-Host "✓ Projet nettoyé" -ForegroundColor Green
    } catch {
        Write-Host "❌ Erreur lors du nettoyage" -ForegroundColor Red
        Write-Host $_.Exception.Message -ForegroundColor Red
        exit 1
    }
    
    Write-Host "📦 Récupération des dépendances..." -ForegroundColor Yellow
    
    try {
        flutter pub get | Out-Null
        Write-Host "✓ Dépendances récupérées" -ForegroundColor Green
    } catch {
        Write-Host "❌ Erreur lors de la récupération des dépendances" -ForegroundColor Red
        Write-Host $_.Exception.Message -ForegroundColor Red
        exit 1
    }
    
    Write-Host "🔨 Compilation de l'APK release..." -ForegroundColor Yellow
    Write-Host "⏳ Cela peut prendre plusieurs minutes..." -ForegroundColor Gray
    
    try {
        $buildOutput = flutter build apk --release 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host ""
            Write-Host "🎉 Compilation réussie !" -ForegroundColor Green
            Write-Host ""
            Write-Host "📱 APK généré: build\app\outputs\flutter-apk\app-release.apk" -ForegroundColor Cyan
            
            # Vérifier la taille de l'APK
            $apkPath = "build\app\outputs\flutter-apk\app-release.apk"
            if (Test-Path $apkPath) {
                $apkSize = (Get-Item $apkPath).Length
                $apkSizeMB = [math]::Round($apkSize / 1MB, 1)
                Write-Host "📊 Taille de l'APK: $apkSizeMB MB" -ForegroundColor Cyan
            }
            
            Write-Host ""
            Write-Host "🚀 Prêt pour installation sur Samsung Galaxy S23 Ultra !" -ForegroundColor Green
            Write-Host ""
            Write-Host "💡 Utilisez install_on_phone.ps1 pour installer automatiquement" -ForegroundColor Yellow
            
        } else {
            Write-Host ""
            Write-Host "❌ Erreur lors de la compilation" -ForegroundColor Red
            Write-Host $buildOutput -ForegroundColor Red
            exit 1
        }
    } catch {
        Write-Host ""
        Write-Host "❌ Erreur lors de la compilation" -ForegroundColor Red
        Write-Host $_.Exception.Message -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host ""
    Write-Host "ℹ️ Compilation annulée" -ForegroundColor Gray
    Write-Host "Vous pouvez compiler manuellement avec: flutter build apk --release" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "✨ Script terminé" -ForegroundColor Green
Write-Host ""
Write-Host "Appuyez sur une touche pour continuer..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
