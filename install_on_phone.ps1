# Script d'installation SeaTrace pour Samsung Galaxy S23 Ultra
# Assurez-vous que votre téléphone est connecté via USB avec le débogage activé

Write-Host "=== Installation SeaTrace sur Samsung Galaxy S23 Ultra ===" -ForegroundColor Green
Write-Host ""

# Vérifier si ADB est disponible
try {
    $adbVersion = adb version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "ADB non trouvé"
    }
    Write-Host "✓ ADB détecté" -ForegroundColor Green
} catch {
    Write-Host "❌ ADB n'est pas installé ou n'est pas dans le PATH" -ForegroundColor Red
    Write-Host "Veuillez installer Android SDK Platform Tools" -ForegroundColor Yellow
    Write-Host "Ou utilisez la méthode d'installation manuelle (voir INSTALLATION_GUIDE.md)" -ForegroundColor Yellow
    exit 1
}

# Vérifier la connexion du téléphone
Write-Host "Vérification de la connexion du téléphone..." -ForegroundColor Yellow
$devices = adb devices
if ($devices -match "device$") {
    Write-Host "✓ Téléphone connecté et détecté" -ForegroundColor Green
} else {
    Write-Host "❌ Aucun téléphone détecté" -ForegroundColor Red
    Write-Host "Assurez-vous que :" -ForegroundColor Yellow
    Write-Host "  1. Le téléphone est connecté via USB" -ForegroundColor Yellow
    Write-Host "  2. Le débogage USB est activé" -ForegroundColor Yellow
    Write-Host "  3. Vous avez autorisé la connexion sur le téléphone" -ForegroundColor Yellow
    exit 1
}

# Vérifier l'existence du fichier APK
$apkPath = "build\app\outputs\flutter-apk\app-release.apk"
if (Test-Path $apkPath) {
    Write-Host "✓ Fichier APK trouvé : $apkPath" -ForegroundColor Green
} else {
    Write-Host "❌ Fichier APK non trouvé : $apkPath" -ForegroundColor Red
    Write-Host "Veuillez d'abord compiler l'application avec : flutter build apk --release" -ForegroundColor Yellow
    exit 1
}

# Installation de l'APK
Write-Host ""
Write-Host "Installation de SeaTrace en cours..." -ForegroundColor Yellow
try {
    adb install -r $apkPath
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "🎉 Installation réussie !" -ForegroundColor Green
        Write-Host ""
        Write-Host "L'application SeaTrace est maintenant installée sur votre Samsung Galaxy S23 Ultra" -ForegroundColor Green
        Write-Host ""
        Write-Host "Comptes de test disponibles :" -ForegroundColor Cyan
        Write-Host "  Pêcheur     : <EMAIL> / password" -ForegroundColor White
        Write-Host "  Vétérinaire : <EMAIL> / password" -ForegroundColor White
        Write-Host "  Mareyeur    : <EMAIL> / password" -ForegroundColor White
        Write-Host "  Client      : <EMAIL> / password" -ForegroundColor White
        Write-Host ""
        Write-Host "Vous pouvez maintenant lancer l'application depuis votre téléphone !" -ForegroundColor Green
    } else {
        throw "Erreur lors de l'installation"
    }
} catch {
    Write-Host "❌ Erreur lors de l'installation" -ForegroundColor Red
    Write-Host "Essayez l'installation manuelle (voir INSTALLATION_GUIDE.md)" -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "Appuyez sur une touche pour continuer..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
